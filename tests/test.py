import unittest
import datetime
from v_switch.db import init_db
from v_switch.db.eip_binding import Eip<PERSON>ind<PERSON>, create_eip_binding, get_eip_binding, update_eip_binding, delete_eip_binding
from v_switch.db.eip_dnat import EipDnat, create_eip_dnat, get_eip_dnat, update_eip_dnat, delete_eip_dnat
from v_switch.db.eip_snat import EipSnat, create_eip_snat, get_eip_snat, update_eip_snat, delete_eip_snat
from v_switch.db.ip_pool import IpPool, create_ip_pool, get_ip_pool, update_ip_pool, delete_ip_pool
from v_switch.db.ip_pool_allocated import IpPoolAllocated, create_ip_pool_allocated, get_ip_pool_allocated, update_ip_pool_allocated, delete_ip_pool_allocated
from v_switch.db.network import Network, create_network, get_network, update_network, delete_network
from v_switch.db.operation_log import OperationLog, create_operation_log, get_operation_log
from v_switch.db.raft_status import Raft<PERSON>tat<PERSON>, create_raft_status, get_raft_status, update_raft_status, delete_raft_status


class TestEipBinding(unittest.TestCase):
    def test_crud(self):
        # Test create
        eip_binding = EipBinding(eip="*******", eip_vlan_id=100, internal_ip="***********", sub_vlan_id=200, rate=1000, ceil=2000)
        created = create_eip_binding(eip_binding)
        self.assertIsNotNone(created.id)
        self.assertEqual(created.eip, "*******")

        # Test read
        retrieved = get_eip_binding(created.id)
        self.assertEqual(retrieved.id, created.id)

        # Test update
        update_data = {"rate": 1500}
        update_eip_binding(created.id, update_data)
        updated = get_eip_binding(created.id)
        self.assertEqual(updated.rate, 1500)

        # Test delete
        delete_eip_binding(created.id)
        retrieved_after_delete = get_eip_binding(created.id)
        self.assertIsNone(retrieved_after_delete)


class TestEipDnat(unittest.TestCase):
    def test_crud(self):
        # Test create
        eip_dnat = EipDnat(subnet_id=1, eip="*******", internal_ip="***********")
        created = create_eip_dnat(eip_dnat)
        self.assertIsNotNone(created.id)
        self.assertEqual(created.eip, "*******")

        # Test read
        retrieved = get_eip_dnat(created.id)
        self.assertEqual(retrieved.id, created.id)

        # Test update
        update_data = {"eip": "*******"}
        update_eip_dnat(created.id, update_data)
        updated = get_eip_dnat(created.id)
        self.assertEqual(updated.eip, "*******")

        # Test delete
        delete_eip_dnat(created.id)
        retrieved_after_delete = get_eip_dnat(created.id)
        self.assertIsNone(retrieved_after_delete)


class TestEipSnat(unittest.TestCase):
    def test_crud(self):
        # Test create
        eip_snat = EipSnat(subnet_id=1, eip="*******", gateway_ip="********", mask="*************")
        created = create_eip_snat(eip_snat)
        self.assertIsNotNone(created.id)
        self.assertEqual(created.eip, "*******")

        # Test read
        retrieved = get_eip_snat(created.id)
        self.assertEqual(retrieved.id, created.id)

        # Test update
        update_data = {"eip": "*******"}
        update_eip_snat(created.id, update_data)
        updated = get_eip_snat(created.id)
        self.assertEqual(updated.eip, "*******")

        # Test delete
        delete_eip_snat(created.id)
        retrieved_after_delete = get_eip_snat(created.id)
        self.assertIsNone(retrieved_after_delete)


class TestIpPool(unittest.TestCase):
    def test_crud(self):
        # Test create
        ip_pool = IpPool(name="test_pool", type="public", ip_start="*******", ip_end="*******0", vlan_id=100)
        created = create_ip_pool(ip_pool)
        self.assertIsNotNone(created.id)
        self.assertEqual(created.name, "test_pool")

        # Test read
        retrieved = get_ip_pool(created.id)
        self.assertEqual(retrieved.id, created.id)

        # Test update
        update_data = {"name": "new_test_pool"}
        update_ip_pool(created.id, update_data)
        updated = get_ip_pool(created.id)
        self.assertEqual(updated.name, "new_test_pool")

        # Test delete
        delete_ip_pool(created.id)
        retrieved_after_delete = get_ip_pool(created.id)
        self.assertIsNone(retrieved_after_delete)


class TestIpPoolAllocated(unittest.TestCase):
    def test_crud(self):
        # Test create
        ip_pool_allocated = IpPoolAllocated(pool_id=1, pool_type="public", ip="*******", status="allocated")
        created = create_ip_pool_allocated(ip_pool_allocated)
        self.assertIsNotNone(created.id)
        self.assertEqual(created.ip, "*******")

        # Test read
        retrieved = get_ip_pool_allocated(created.id)
        self.assertEqual(retrieved.id, created.id)

        # Test update
        update_data = {"status": "reclaimed"}
        update_ip_pool_allocated(created.id, update_data)
        updated = get_ip_pool_allocated(created.id)
        self.assertEqual(updated.status, "reclaimed")

        # Test delete
        delete_ip_pool_allocated(created.id)
        retrieved_after_delete = get_ip_pool_allocated(created.id)
        self.assertIsNone(retrieved_after_delete)


class TestNetwork(unittest.TestCase):
    def test_crud(self):
        # Test create
        network = Network(server_type="vm", tenant_id="tenant1", vlan_id=101, ip_start="***********", ip_end="***********00", ip_mask="*************", ip_gateway="*************")
        created = create_network(network)
        self.assertIsNotNone(created.id)
        self.assertEqual(created.tenant_id, "tenant1")

        # Test read
        retrieved = get_network(created.id)
        self.assertEqual(retrieved.id, created.id)

        # Test update
        update_data = {"tenant_id": "tenant2"}
        update_network(created.id, update_data)
        updated = get_network(created.id)
        self.assertEqual(updated.tenant_id, "tenant2")

        # Test delete
        delete_network(created.id)
        retrieved_after_delete = get_network(created.id)
        self.assertIsNone(retrieved_after_delete)


class TestOperationLog(unittest.TestCase):
    def test_create_and_get(self):
        # Test create
        log_data = {"key": "value"}
        operation_log = OperationLog(node_id=1, index=1, type="create", object="network", data=log_data, status="success")
        created = create_operation_log(operation_log)
        self.assertIsNotNone(created.id)
        self.assertEqual(created.object, "network")

        # Test read
        retrieved = get_operation_log(created.id)
        self.assertEqual(retrieved.id, created.id)
        self.assertEqual(retrieved.data, log_data)


class TestRaftStatus(unittest.TestCase):
    def test_crud(self):
        # Test create
        raft_status = RaftStatus(node_id=1, group_id="group1", endpoint="localhost:50051", role="leader", status="running", term=1, commit_index=1, last_applied=1)
        created = create_raft_status(raft_status)
        self.assertIsNotNone(created.id)
        self.assertEqual(created.role, "leader")

        # Test read
        retrieved = get_raft_status(created.id)
        self.assertEqual(retrieved.id, created.id)

        # Test update
        update_data = {"role": "follower"}
        update_raft_status(created.id, update_data)
        updated = get_raft_status(created.id)
        self.assertEqual(updated.role, "follower")

        # Test delete
        delete_raft_status(created.id)
        retrieved_after_delete = get_raft_status(created.id)
        self.assertIsNone(retrieved_after_delete)


if __name__ == '__main__':
    print("Initializing database...")
    init_db()
    print("Database initialized.")
    print("Running tests...")
    unittest.main()
