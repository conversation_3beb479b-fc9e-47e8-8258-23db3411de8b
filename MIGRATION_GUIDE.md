# V-Switch Database Migration Guide

This guide explains how to migrate from the etcd-based v-switch system to the new MySQL database-based system.

## Prerequisites

1. **MySQL/MariaDB Database**: Ensure you have a running MySQL or MariaDB instance
2. **Python Dependencies**: Install required packages:
   ```bash
   pip install pymysql sqlalchemy flask requests
   ```
3. **Database Access**: Ensure the application has proper database credentials

## Migration Steps

### Step 1: Database Setup

1. **Create Database**:
   ```sql
   CREATE DATABASE `cloudlink-sdn` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'admin'@'%' IDENTIFIED BY 'admin';
   GRANT ALL PRIVILEGES ON `cloudlink-sdn`.* TO 'admin'@'%';
   FLUSH PRIVILEGES;
   ```

2. **Update Database Configuration**:
   Edit `src/v_switch/db/__init__.py` to match your database settings:
   ```python
   DATABASE_URL = "mysql+pymysql://admin:admin@*************:3306/cloudlink-sdn"
   ```

3. **Initialize Database Tables**:
   ```python
   from v_switch.db import init_db
   init_db()
   ```

### Step 2: Update Agent Configuration

1. **Update Agent Config Files**:
   Change from:
   ```yaml
   agent:
     agent_id: agent-1
     heartbeat_interval: 30.0
   ```
   
   To:
   ```yaml
   agent:
     node_id: node-1          # Changed from agent_id
     group_id: group-0        # New field
     endpoint: *************:8080  # New field
     heartbeat_interval: 30.0
   ```

2. **Update Agent Startup Scripts**:
   Ensure agents use the new configuration format and start the HTTP server.

### Step 3: Update Core Service

1. **Database Service Integration**:
   The core service now uses `DatabaseService` for data operations instead of direct etcd calls.

2. **Agent Communication**:
   Core service now uses `DatabaseAgentManager` to communicate with agents via HTTP instead of etcd.

### Step 4: API Server Updates

1. **Heartbeat Endpoint**:
   The API server now includes a `/api/heartbeat` endpoint for agent heartbeats.

2. **No External API Changes**:
   External API endpoints remain the same for backward compatibility.

## Configuration Examples

### Complete Agent Configuration
```yaml
agent:
  node_id: node-1
  group_id: group-0
  endpoint: *************:8080
  heartbeat_interval: 30.0
  max_retries: 3

etcd:
  host: *************
  port: 30379
  timeout: 5.0
  username: ''
  password: ''

logging:
  level: INFO
  file: ''

env_check:
  base: true
  ovs: true
  nftables: true
```

### Database Connection Configuration
```python
# In src/v_switch/db/__init__.py
DATABASE_URL = "mysql+pymysql://username:password@host:port/database"
connect_args = {
    "charset": "utf8",
    "ssl_disabled": True,
    "init_command": "SET time_zone='Asia/Shanghai'",
}
```

## Testing the Migration

### 1. Run the Demo Script
```bash
python scripts/demo_database_migration.py
```

### 2. Test Database Operations
```python
from v_switch.core_service.database_service import DatabaseService
from v_switch.core_service.database_agent_manager import DatabaseAgentManager

# Test database service
db_service = DatabaseService()
subnet_data = {
    'server_type': 'guest',
    'ip_start': '*************0',
    'ip_end': '***************',
    'ip_gateway': '*************',
    'ip_mask': 24
}
subnet_id = db_service.create_subnet_record('test-tenant', 1500, subnet_data)

# Test agent manager
agent_manager = DatabaseAgentManager()
group_id = agent_manager.get_group_id_for_vlan(1500)
agents = agent_manager.get_agents_for_vlan(1500)
```

### 3. Test Agent Registration
```python
from v_switch.db.node_status import register_or_update_node

# Register a test node
node = register_or_update_node('test-node-1', 'group-0', '*************:8080')
print(f"Registered node: {node.node_id}")
```

## Operational Workflow

### Core Service Operation Flow
1. **API Request** → Core service receives request
2. **Database Write** → Write to MySQL (network, eip_binding, etc.)
3. **Redo Log** → Log operation to redo_log table
4. **Agent Selection** → Calculate group_id from vlan_id % 4
5. **Agent Notification** → HTTP POST to agents in group
6. **Response** → Return to client

### Agent Startup Flow
1. **Load Config** → Read node_id, group_id, endpoint
2. **Start HTTP Server** → Listen for operations on endpoint
3. **Register Node** → Send heartbeat to register in database
4. **Sync Operations** → Get missed operations from redo_log
5. **Start Heartbeat** → Send periodic heartbeats to API server

### Agent Operation Flow
1. **Receive Operation** → HTTP POST to /api/operation/execute
2. **Execute Operation** → Process create/update/delete
3. **Update Status** → Update last_applied via heartbeat
4. **Respond** → Return success/failure

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database credentials and connectivity
   - Ensure database exists and user has proper permissions
   - Verify firewall settings

2. **Agent Registration Failed**
   - Check agent configuration (node_id, group_id, endpoint)
   - Verify API server is running and accessible
   - Check network connectivity between agent and API server

3. **Operation Sync Failed**
   - Check redo_log table for entries
   - Verify agent's last_applied value in node_status table
   - Check agent logs for sync errors

4. **HTTP Server Start Failed**
   - Check if port is already in use
   - Verify endpoint configuration matches actual binding
   - Check firewall settings for agent ports

### Debugging Commands

```bash
# Check database tables
mysql -u admin -p cloudlink-sdn -e "SHOW TABLES;"

# Check node status
mysql -u admin -p cloudlink-sdn -e "SELECT * FROM node_status;"

# Check redo logs
mysql -u admin -p cloudlink-sdn -e "SELECT * FROM redo_log ORDER BY id DESC LIMIT 10;"

# Test agent endpoint
curl http://*************:8080/health

# Test API server heartbeat
curl -X POST http://localhost:30090/api/heartbeat \
  -H "Content-Type: application/json" \
  -d '{"node_id":"test-node","group_id":"group-0","endpoint":"*************:8080"}'
```

## Rollback Plan

If you need to rollback to the etcd-based system:

1. **Stop New Services**: Stop agents using the new configuration
2. **Restore Etcd Data**: Restore etcd data from backup if needed
3. **Revert Configuration**: Use old agent configuration files
4. **Restart Services**: Start services with old configuration

## Performance Considerations

1. **Database Indexing**: Add indexes for frequently queried fields:
   ```sql
   CREATE INDEX idx_node_status_group_id ON node_status(group_id);
   CREATE INDEX idx_node_status_node_id ON node_status(node_id);
   CREATE INDEX idx_redo_log_id ON redo_log(id);
   ```

2. **Connection Pooling**: Configure appropriate connection pool settings
3. **Monitoring**: Monitor database performance and connection usage
4. **Backup**: Set up regular database backups

## Next Steps

1. **Production Deployment**: Deploy to production environment
2. **Monitoring Setup**: Add metrics and alerting
3. **Performance Tuning**: Optimize based on actual usage patterns
4. **Documentation**: Update operational documentation
5. **Training**: Train operations team on new system
