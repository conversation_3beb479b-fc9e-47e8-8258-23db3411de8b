# V-Switch Database Migration Summary

This document summarizes the changes made to migrate the v-switch system from etcd-based storage to MySQL database storage, as specified in the v2 design document.

## Overview

The migration involves:
1. Moving subnet, EIP mount, SNAT, and DNAT data from etcd to MySQL database
2. Implementing a redo log system for operation tracking
3. Updating agent management to use database-based node status
4. Implementing new heartbeat mechanism via API server
5. Adding agent synchronization based on redo logs

## Database Changes

### Updated Models

#### 1. `src/v_switch/db/redo_log.py`
- **Updated fields**: Removed `index` field, changed `time` to BigInteger timestamp
- **Added functions**: 
  - `get_redo_logs_after_id()` - Get logs after a specific ID for agent sync
  - `get_latest_redo_log_id()` - Get the latest log ID

#### 2. `src/v_switch/db/node_status.py`
- **Updated fields**: Changed `node_id` to String with unique constraint, added proper defaults
- **Added functions**:
  - `get_node_status_by_node_id()` - Get node by node_id
  - `get_nodes_by_group_id()` - Get all nodes in a group
  - `update_node_heartbeat()` - Update heartbeat timestamp
  - `update_node_last_applied()` - Update last applied redo log ID
  - `register_or_update_node()` - Register new node or update existing

#### 3. `src/v_switch/db/__init__.py`
- **Updated imports**: Added redo_log and node_status to database initialization

## Core Service Changes

### New Components

#### 1. `src/v_switch/core_service/database_service.py`
- **Purpose**: Provides CRUD operations for subnet, EIP, SNAT, DNAT data using MySQL
- **Key methods**:
  - `create_subnet_record()` - Create subnet in database
  - `create_eip_mount_record()` - Create EIP binding
  - `create_snat_record()` - Create SNAT rule
  - `create_dnat_record()` - Create DNAT rule
  - `log_operation()` - Log operations to redo_log table

#### 2. `src/v_switch/core_service/database_agent_manager.py`
- **Purpose**: Database-based agent management and communication
- **Key methods**:
  - `get_group_id_for_vlan()` - Calculate group ID using vlan_id % total_groups
  - `get_agents_for_vlan()` - Get agents responsible for a VLAN
  - `notify_agents_of_operation()` - Notify agents of operations via HTTP
  - `get_agent_sync_data()` - Get sync data for agent startup

### Updated Components

#### 1. `src/v_switch/core_service/core_service.py`
- **Added imports**: Database service and agent manager
- **Added initialization**: Database service and database agent manager
- **Added method**: `update_agent_heartbeat()` - Handle agent heartbeat updates

## Agent Changes

### Configuration Updates

#### 1. `src/v_switch/config/agent_config.py`
- **Updated AgentSettings**: 
  - Changed `agent_id` to `node_id`
  - Added `group_id` field
  - Added `endpoint` field
  - Maintained backward compatibility with `agent_id` property

#### 2. `config/agent_config.yaml.example`
- **Updated example**: Shows new configuration format with node_id, group_id, endpoint

### Migrated Components

#### 1. `src/v_switch/agent/agent.py` (COMPLETELY MIGRATED)
- **Purpose**: Main agent class completely migrated from etcd to database-based architecture
- **Major Changes**:
  - ❌ **Removed**: All etcd client dependencies and imports
  - ❌ **Removed**: etcd-based metadata and shard monitoring (`_monitor_metadata`, `_update_assigned_shards`)
  - ❌ **Removed**: etcd-based subnet watching (`_monitor_shard_subnets`, `_handle_subnet_config_change`)
  - ❌ **Removed**: etcd-based task management (`_process_subnet_config`)
  - ❌ **Removed**: Complex startup sequence with metadata waiting
  - ✅ **Added**: HTTP server for receiving operations from core_service
  - ✅ **Added**: Database heartbeat manager for API-based heartbeat
  - ✅ **Added**: Operation handler for processing HTTP requests
  - ✅ **Added**: Simplified 3-step startup: HTTP server → heartbeat → sync
- **New Architecture**:
  - Uses `DatabaseHeartbeatManager` instead of etcd-based `HeartbeatManager`
  - Uses `OperationHandler` for processing operations from HTTP requests
  - Uses `AgentHttpServer` (FastAPI) for receiving operations from core_service
  - Maintains `LocalDataManager` and `CommandProcessor` for local operations
  - Removed all threading for etcd watching and metadata monitoring

### New Components

#### 1. `src/v_switch/agent/database_heartbeat_manager.py`
- **Purpose**: Send heartbeat to API server instead of etcd
- **Key methods**:
  - `_send_heartbeat()` - Send HTTP POST to /api/heartbeat
  - `update_last_applied()` - Update last applied redo log ID
  - `register_node()` - Register node on startup

#### 2. `src/v_switch/agent/operation_handler.py`
- **Purpose**: Handle operations received from core_service
- **Key methods**:
  - `handle_operation()` - Process single operation and update last_applied
  - `sync_operations_on_startup()` - Sync missed operations on startup
  - `_execute_operation()` - Execute specific operation types

#### 3. `src/v_switch/agent/http_server.py`
- **Purpose**: FastAPI-based HTTP server to receive operations from core_service
- **Framework**: Migrated from Flask to FastAPI for consistency with main API server
- **Features**:
  - Automatic request/response validation with Pydantic models
  - Built-in API documentation (Swagger UI at `/docs`)
  - Better type hints and async support
  - Consistent error handling with HTTPException
- **Endpoints**:
  - `POST /api/operation/execute` - Execute operation
  - `POST /api/sync` - Sync operations
  - `GET /health` - Health check
  - `GET /api/status` - Get agent status

## API Server Changes

### New Endpoints

#### 1. `src/v_switch/api_server/api_server.py`
- **Added model**: `HeartbeatRequest` - Request model for agent heartbeat
- **Added endpoint**: `POST /api/heartbeat` - Receive agent heartbeat and update database

## Workflow Changes

### Core Service Operation Flow (New)
1. **Operation Request** → Core service receives API request
2. **Database Write** → Write data to MySQL database (network, eip_binding, etc.)
3. **Redo Log** → Log operation to redo_log table
4. **Agent Selection** → Calculate group_id from vlan_id % total_groups
5. **Agent Notification** → HTTP POST to agents in the group
6. **Response** → Return success/failure to API client

### Agent Startup Flow (New - Completely Migrated)
1. **Start HTTP Server** → Start FastAPI server on configured endpoint to receive operations
2. **Start Heartbeat Manager** → Register node in database and start periodic API heartbeat
3. **Sync Operations** → Get missed operations from redo_log table where id > last_applied
4. **Ready for Operations** → Agent ready to receive HTTP operations from core_service

**Key Changes from Old etcd-based Flow**:
- ❌ **Removed**: etcd connection and complex metadata watching
- ❌ **Removed**: Shard assignment and subnet monitoring threads
- ❌ **Removed**: etcd-based task creation and management
- ✅ **Added**: HTTP server startup for operation reception
- ✅ **Added**: Database-based node registration via API calls
- ✅ **Added**: Redo log-based operation synchronization

### Agent Operation Flow (New)
1. **Receive Operation** → HTTP POST to /api/operation/execute
2. **Execute Operation** → Process the operation (create/update/delete)
3. **Update Status** → Update last_applied in node_status table via heartbeat
4. **Response** → Return success/failure to core_service

## Migration Benefits

1. **Scalability**: MySQL database can handle larger datasets than etcd
2. **Reliability**: ACID transactions ensure data consistency
3. **Monitoring**: Redo log provides complete operation history
4. **Synchronization**: Agents can sync missed operations on startup
5. **Group Management**: Automatic agent selection based on VLAN ID
6. **API Consistency**: Both API server and agent use FastAPI for consistent development experience
7. **Better Validation**: Pydantic models provide automatic request/response validation
8. **Documentation**: Automatic API documentation generation with Swagger UI
9. **Performance**: FastAPI provides better performance than Flask
10. **Type Safety**: Better type hints and IDE support throughout the system

## Next Steps

1. **Testing**: Create comprehensive tests for the new database-based system
2. **Migration Script**: Create script to migrate existing etcd data to MySQL
3. **Production Deployment**: Update deployment scripts and documentation
4. **Monitoring**: Add metrics and monitoring for the new system
5. **Performance Tuning**: Optimize database queries and indexes

## Backward Compatibility

- Agent configuration supports both `agent_id` and `node_id` fields
- Core service maintains both etcd and database-based agent managers
- API endpoints remain unchanged for external clients

## Configuration Examples

### Agent Configuration (New Format)
```yaml
agent:
  node_id: node-1
  group_id: group-0
  endpoint: *************:8080
  heartbeat_interval: 30.0
  max_retries: 3
```

### Database Connection
```python
DATABASE_URL = "mysql+pymysql://admin:admin@*************:3306/cloudlink-sdn"
```

This migration provides a solid foundation for the v2 architecture while maintaining compatibility with existing systems.
