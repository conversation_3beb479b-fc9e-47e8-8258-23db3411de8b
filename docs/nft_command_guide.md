# nftables nft 命令详细使用指南

## 概述

nftables 是 Linux 内核的新一代包过滤框架，旨在替代 iptables、ip6tables、arptables 和 ebtables。nft 是 nftables 的用户空间命令行工具，提供统一的语法来配置网络包过滤规则。

## 1. nft 命令的构成结构

### 1.1 基本语法结构

```bash
nft [选项] 命令 [对象] [参数]
```

### 1.2 命令组件详解

#### 1.2.1 选项 (Options)
- `-f, --file <文件名>`: 从文件读取命令
- `-i, --interactive`: 进入交互模式
- `-c, --check`: 仅检查语法，不执行命令
- `-n, --numeric`: 以数字形式显示地址和端口
- `-a, --handle`: 显示规则句柄
- `-s, --stateless`: 省略有状态信息
- `-j, --json`: 以JSON格式输出
- `-y, --numeric-priority`: 以数字形式显示优先级
- `-N, --reversedns`: 反向解析IP地址为主机名
- `-S, --service`: 将端口号解析为服务名

#### 1.2.2 命令 (Commands)
- `add`: 添加新对象
- `create`: 创建新对象（如果已存在则失败）
- `delete`: 删除对象
- `list`: 列出对象
- `flush`: 清空对象内容
- `rename`: 重命名对象
- `replace`: 替换现有规则
- `insert`: 在指定位置插入规则
- `monitor`: 监控netlink事件

#### 1.2.3 对象 (Objects)
nftables 采用分层结构：

```
表 (table)
├── 链 (chain)
│   ├── 规则 (rule)
│   │   ├── 匹配条件 (match)
│   │   └── 动作 (verdict)
│   └── ...
├── 集合 (set)
├── 映射 (map)
└── 计数器 (counter)
```

### 1.3 对象结构详解

#### 1.3.1 表 (Table)
表是最顶层的容器，按协议族组织：

```bash
# 创建表
nft add table [family] <table_name>

# 协议族类型
- ip       # IPv4 (默认)
- ip6      # IPv6  
- inet     # IPv4 和 IPv6
- arp      # ARP
- bridge   # 桥接
- netdev   # 网络设备
```

#### 1.3.2 链 (Chain)
链包含规则的有序列表：

```bash
# 创建链
nft add chain [family] <table> <chain> { [chain_spec] }

# 链规格参数
- type <类型>      # 链类型
- hook <钩子>      # 内核钩子点
- priority <优先级> # 处理优先级
- policy <策略>    # 默认策略
```

#### 1.3.3 规则 (Rule)
规则定义匹配条件和相应动作：

```bash
# 添加规则
nft add rule [family] <table> <chain> [handle <handle>] <expression>

# 规则表达式结构
<expression> = <match> <verdict>
```

## 2. nft 链类型详解

### 2.1 链类型分类

nftables 的链分为两大类：
- **基链 (Base Chain)**: 连接到内核钩子点的链
- **常规链 (Regular Chain)**: 用户自定义的链，由其他链跳转调用

### 2.2 基链类型

#### 2.2.1 filter 类型
用于包过滤，支持所有钩子点。

**支持的钩子点:**
```bash
# IPv4/IPv6/inet 协议族
- prerouting   # 路由前处理
- input        # 本地输入
- forward      # 转发
- output       # 本地输出  
- postrouting  # 路由后处理

# bridge 协议族额外支持
- brouting     # 桥接路由决策
```

**优先级范围:** -300 到 300

**示例:**
```bash
nft add table inet mytable
nft add chain inet mytable input { 
    type filter hook input priority 0; policy accept; 
}
```

#### 2.2.2 nat 类型
用于网络地址转换。

**支持的钩子点:**
```bash
- prerouting   # DNAT (目标NAT)
- input        # 本地输入NAT
- output       # 本地输出NAT
- postrouting  # SNAT (源NAT)
```

**优先级:** 通常为 -100 (dstnat) 或 100 (srcnat)

**示例:**
```bash
nft add chain inet nat prerouting { 
    type nat hook prerouting priority -100; 
}
```

#### 2.2.3 route 类型
用于重新路由包（仅支持 output 钩子）。

**支持的钩子点:**
```bash
- output       # 仅支持输出钩子
```

**示例:**
```bash
nft add chain ip mangle output { 
    type route hook output priority -150; 
}
```

#### 2.2.4 netdev 类型
在网络设备层面进行早期处理。

**支持的钩子点:**
```bash
- ingress      # 设备入口
- egress       # 设备出口
```

**示例:**
```bash
nft add table netdev filter
nft add chain netdev filter ingress { 
    type filter hook ingress device eth0 priority 0; 
}
```

### 2.3 链优先级

优先级决定链的执行顺序（数值越小越先执行）：

```bash
# 常用优先级
NF_IP_PRI_FIRST           -**********
NF_IP_PRI_CONNTRACK_DEFRAG    -400
NF_IP_PRI_RAW                 -300
NF_IP_PRI_SELINUX_FIRST       -225
NF_IP_PRI_CONNTRACK           -200
NF_IP_PRI_MANGLE              -150
NF_IP_PRI_NAT_DST             -100
NF_IP_PRI_FILTER                 0
NF_IP_PRI_SECURITY               50
NF_IP_PRI_NAT_SRC               100
NF_IP_PRI_SELINUX_LAST          225
NF_IP_PRI_CONNTRACK_HELPER      300
NF_IP_PRI_LAST           2147483647
```

## 3. 匹配条件详解

### 3.1 网络层匹配

#### 3.1.1 IP 协议匹配
```bash
# 源/目标地址
ip saddr <地址>          # 源IP地址
ip daddr <地址>          # 目标IP地址
ip saddr & daddr <地址>  # 源和目标地址

# 协议类型
ip protocol <协议>       # IP协议号
ip6 nexthdr <协议>       # IPv6下一头部

# TTL/跳数限制
ip ttl <值>              # IPv4 TTL
ip6 hoplimit <值>        # IPv6跳数限制

# 包长度
ip length <长度>         # IP包总长度
```

#### 3.1.2 地址表示方法
```bash
# 单个地址
***********
2001:db8::1

# CIDR 网段
***********/24
2001:db8::/32

# 地址范围
***********-*************

# 地址集合
{ ***********, ***********, *********** }
```

### 3.2 传输层匹配

#### 3.2.1 TCP 匹配
```bash
tcp sport <端口>         # TCP源端口
tcp dport <端口>         # TCP目标端口  
tcp flags <标志>         # TCP标志位
tcp window <窗口大小>    # TCP窗口大小
tcp checksum <校验和>    # TCP校验和
tcp urgptr <紧急指针>    # TCP紧急指针

# TCP 标志位
syn, ack, fin, rst, psh, urg, ecn, cwr
```

#### 3.2.2 UDP 匹配
```bash
udp sport <端口>         # UDP源端口
udp dport <端口>         # UDP目标端口
udp length <长度>        # UDP长度
udp checksum <校验和>    # UDP校验和
```

#### 3.2.3 端口表示方法
```bash
# 单个端口
80
ssh  # 服务名

# 端口范围
1024-65535
1000-2000

# 端口集合
{ 80, 443, 8080 }
```

### 3.3 连接跟踪匹配

#### 3.3.1 连接状态
```bash
ct state <状态>          # 连接状态
ct direction <方向>      # 连接方向
ct mark <标记>           # 连接标记
ct label <标签>          # 连接标签

# 连接状态类型
new        # 新连接
established # 已建立的连接
related    # 相关连接
invalid    # 无效连接
untracked  # 未跟踪连接
```

### 3.4 接口匹配
```bash
iif <接口>               # 输入接口
iifname <接口名>         # 输入接口名
oif <接口>               # 输出接口  
oifname <接口名>         # 输出接口名

# 接口匹配示例
iifname "eth0"
iifname { "eth0", "eth1" }
iifname "eth*"           # 通配符匹配
```

### 3.5 元信息匹配
```bash
meta iif <接口索引>      # 输入接口索引
meta oif <接口索引>      # 输出接口索引  
meta mark <标记>         # 包标记
meta priority <优先级>   # 包优先级
meta skuid <UID>         # 发送用户ID
meta skgid <GID>         # 发送组ID
meta nftrace <值>        # 跟踪标记
```

### 3.6 时间匹配
```bash
# 需要 xt_time 模块支持
meta hour <小时>         # 小时 (0-23)
meta day <星期>          # 星期 (0-6, 0=周日)
```

## 4. 动作 (Verdict) 详解

### 4.1 终结性动作

#### 4.1.1 基本动作
```bash
accept          # 接受包，停止处理
drop            # 丢弃包，静默丢弃
reject          # 拒绝包，发送错误消息
return          # 返回调用链
```

#### 4.1.2 reject 详细选项
```bash
# ICMP 拒绝类型
reject with icmp type host-unreachable
reject with icmp type net-unreachable  
reject with icmp type prot-unreachable
reject with icmp type port-unreachable
reject with icmp type admin-prohibited

# ICMPv6 拒绝类型
reject with icmpv6 type no-route
reject with icmpv6 type admin-prohibited
reject with icmpv6 type addr-unreachable
reject with icmpv6 type port-unreachable

# TCP 重置
reject with tcp reset
```

### 4.2 非终结性动作

#### 4.2.1 跳转动作
```bash
jump <链名>      # 跳转到链，处理完返回
goto <链名>      # 跳转到链，不返回
```

#### 4.2.2 包修改动作
```bash
# SNAT/DNAT
snat to <地址[:端口]>           # 源NAT
dnat to <地址[:端口]>           # 目标NAT
masquerade [to <端口范围>]      # 伪装（动态SNAT）
redirect [to <端口>]            # 重定向到本地端口

# 包标记
meta mark set <值>              # 设置包标记  
meta priority set <值>          # 设置包优先级
meta nftrace set <值>           # 设置跟踪标记

# 连接标记
ct mark set <值>                # 设置连接标记
ct label set <值>               # 设置连接标签

# 包修改
ip ttl set <值>                 # 设置TTL
ip6 hoplimit set <值>           # 设置跳数限制
```

#### 4.2.3 日志动作
```bash
log                             # 记录日志（默认参数）
log prefix "<前缀>"             # 带前缀的日志
log level <级别>                # 指定日志级别
log flags <标志>                # 日志标志

# 日志级别
emerg, alert, crit, err, warn, notice, info, debug

# 日志标志
tcp sequence, tcp options, ip options, skuid, ether, all
```

#### 4.2.4 计数动作
```bash
counter                         # 匹配计数
counter packets <包数> bytes <字节数>  # 带初始值的计数
```

#### 4.2.5 限速动作
```bash
limit rate <速率>               # 限制速率
limit rate over <速率>          # 超过速率时匹配

# 速率单位
/second, /minute, /hour, /day
# 如: 10/minute, 100/hour

# 带突发的限速
limit rate 10/minute burst 5 packets
```

### 4.3 动作组合

可以在一条规则中使用多个动作：

```bash
# 记录并丢弃
log prefix "DROP: " drop

# 计数并接受  
counter accept

# 标记并跳转
meta mark set 1 jump user_chain

# 限速、记录、接受
limit rate 10/minute log prefix "ACCEPT: " accept
```

## 5. 实用示例

### 5.1 基础防火墙配置

```bash
# 创建表和基础链
nft add table inet filter
nft add chain inet filter input { type filter hook input priority 0; policy drop; }
nft add chain inet filter forward { type filter hook forward priority 0; policy drop; }
nft add chain inet filter output { type filter hook output priority 0; policy accept; }

# 允许回环接口
nft add rule inet filter input iifname lo accept

# 允许已建立的连接
nft add rule inet filter input ct state established,related accept

# 允许SSH
nft add rule inet filter input tcp dport 22 accept

# 允许HTTP/HTTPS
nft add rule inet filter input tcp dport { 80, 443 } accept

# 允许ping
nft add rule inet filter input icmp type echo-request accept
```

### 5.2 NAT 配置

```bash
# 创建NAT表
nft add table inet nat
nft add chain inet nat prerouting { type nat hook prerouting priority -100; }
nft add chain inet nat postrouting { type nat hook postrouting priority 100; }

# 端口转发 (DNAT)
nft add rule inet nat prerouting tcp dport 8080 dnat to *************:80

# 源NAT (伪装)
nft add rule inet nat postrouting oifname eth0 masquerade
```

### 5.3 高级匹配示例

```bash
# 匹配特定时间段的连接
nft add rule inet filter input meta hour 9-17 tcp dport 80 accept

# 匹配特定用户的出站连接
nft add rule inet filter output meta skuid 1000 accept

# 匹配多个条件
nft add rule inet filter input ip saddr ***********/24 tcp dport { 80, 443 } ct state new accept

# 使用集合匹配
nft add set inet filter allowed_ips { type ipv4_addr; }
nft add element inet filter allowed_ips { ***********, *********** }
nft add rule inet filter input ip saddr @allowed_ips accept
```

## 6. 管理命令

### 6.1 查看配置
```bash
# 列出所有表
nft list tables

# 列出指定表的所有链
nft list table inet filter

# 列出指定链的规则
nft list chain inet filter input

# 显示规则句柄
nft -a list table inet filter

# JSON格式输出
nft -j list table inet filter
```

### 6.2 导入导出配置
```bash
# 导出配置
nft list ruleset > /etc/nftables.conf

# 导入配置
nft -f /etc/nftables.conf

# 清空所有规则
nft flush ruleset
```

### 6.3 调试和监控
```bash
# 启用规则跟踪
nft add rule inet filter input meta nftrace set 1

# 监控事件
nft monitor

# 查看计数器
nft list counters
```

## 7. 最佳实践

1. **使用 inet 协议族**: 可同时处理IPv4和IPv6
2. **合理设置优先级**: 确保链的执行顺序正确
3. **使用集合和映射**: 提高大量IP/端口匹配的性能
4. **定期备份配置**: 使用 `nft list ruleset` 导出配置
5. **测试配置**: 使用 `-c` 选项检查语法
6. **逐步部署**: 使用 `policy accept` 然后逐步收紧规则
7. **监控日志**: 合理使用 log 动作进行调试

## 8. 故障排除

### 8.1 常见错误
- 语法错误: 使用 `-c` 选项检查
- 规则不生效: 检查链类型和钩子点
- 性能问题: 优化规则顺序，使用集合

### 8.2 调试技巧
- 使用 `meta nftrace set 1` 启用包跟踪
- 使用 counter 统计匹配的包
- 查看 `/var/log/kern.log` 中的相关信息

这份文档涵盖了 nftables nft 命令的主要功能和使用方法。建议在实际使用中结合具体需求，逐步学习和应用这些概念。