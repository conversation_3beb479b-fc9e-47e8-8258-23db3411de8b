# IP分配器 (IP Allocator)

IP分配器是v-switch核心服务的一个组件，提供IP地址的分配、释放和管理功能，使用ETCD进行数据持久化和分布式协调。

## 功能特性

- **IP地址分配**: 自动分配可用的IP地址
- **IP地址释放**: 释放不再使用的IP地址
- **IP地址回收**: 支持已释放IP地址的重用机制
- **范围管理**: 支持指定IP地址分配范围
- **持久化存储**: 使用ETCD存储分配状态，支持分布式环境
- **状态查询**: 提供分配状态和统计信息查询
- **错误处理**: 完善的错误处理和异常管理

## 架构设计

### 数据结构

IP分配器在ETCD中存储的数据格式：

```json
{
  "ip_min": "*************",
  "ip_max": "*************54", 
  "next_ip": "*************",
  "allocated_ips": ["*************", "*************"],
  "reclaimed_ips": []
}
```

### 核心组件

- **IPAllocator**: 主要的IP分配器类
- **IPAllocatorError**: 基础异常类
- **IPRangeExhaustedError**: IP范围耗尽异常
- **InvalidIPError**: 无效IP地址异常

## 使用方法

### 基本用法

```python
from src.v_switch.common.etcd_client import ETCDClient
from src.v_switch.core_service.ip_allocator import IPAllocator

# 1. 创建ETCD客户端
etcd_client = ETCDClient(host="localhost", port=2379)
etcd_client.connect()

# 2. 创建IP分配器
allocator = IPAllocator(
    ip_min="**************",
    ip_max="**************0", 
    etcd_client=etcd_client,
    key="/network/ip_allocator"
)

# 3. 分配IP地址
try:
    ip = allocator.allocate_ip()
    print(f"分配的IP: {ip}")
except IPRangeExhaustedError:
    print("IP地址范围已耗尽")

# 4. 释放IP地址
if allocator.release_ip(ip):
    print(f"成功释放IP: {ip}")
```

### 高级用法

```python
# 查询分配状态
status = allocator.get_allocation_status()
print(f"总IP数: {status['total_ips']}")
print(f"已分配: {status['allocated_count']}")
print(f"使用率: {status['utilization_rate']:.2f}%")

# 检查IP是否已分配
if allocator.is_ip_allocated("**************"):
    print("IP已分配")

# 获取已分配IP列表
allocated_ips = allocator.get_allocated_ips()
print(f"已分配IP: {allocated_ips}")

# 获取已回收IP列表
reclaimed_ips = allocator.get_reclaimed_ips()
print(f"已回收IP: {reclaimed_ips}")
```

## API参考

### IPAllocator类

#### 构造函数

```python
IPAllocator(ip_min: str, ip_max: str, etcd_client: ETCDClient, key: str)
```

**参数:**
- `ip_min`: 最小IP地址
- `ip_max`: 最大IP地址
- `etcd_client`: ETCD客户端实例
- `key`: ETCD中存储数据的键名

#### 主要方法

##### allocate_ip() -> str
分配一个可用的IP地址。

**返回:** 分配的IP地址字符串

**异常:**
- `IPRangeExhaustedError`: IP地址范围已耗尽
- `IPAllocatorError`: 分配失败

##### release_ip(ip: str) -> bool
释放指定的IP地址。

**参数:**
- `ip`: 要释放的IP地址

**返回:** 成功返回True，失败返回False

##### get_allocation_status() -> Dict[str, Any]
获取IP分配状态信息。

**返回:** 包含以下字段的字典：
- `ip_range`: IP地址范围
- `total_ips`: 总IP数量
- `allocated_count`: 已分配IP数量
- `reclaimed_count`: 已回收IP数量
- `available_count`: 可用IP数量
- `next_ip`: 下一个分配的IP
- `utilization_rate`: 使用率百分比

##### is_ip_allocated(ip: str) -> bool
检查IP地址是否已分配。

**参数:**
- `ip`: 要检查的IP地址

**返回:** 已分配返回True，否则返回False

##### get_allocated_ips() -> List[str]
获取所有已分配的IP地址列表。

**返回:** IP地址字符串列表

##### get_reclaimed_ips() -> List[str]
获取所有已回收的IP地址列表。

**返回:** IP地址字符串列表

## 分配算法

### IP分配逻辑

1. **优先回收**: 首先检查回收列表，如果有可用IP则优先分配
2. **顺序分配**: 如果回收列表为空，从`next_ip`开始顺序查找
3. **跳过已分配**: 自动跳过已分配的IP地址
4. **范围检查**: 确保分配的IP在指定范围内
5. **状态更新**: 更新内存状态和ETCD数据

### IP释放逻辑

1. **有效性检查**: 验证IP地址格式和范围
2. **分配状态检查**: 确认IP确实已分配
3. **状态更新**: 从已分配列表移除，添加到回收列表
4. **持久化**: 更新ETCD中的数据

## 错误处理

### 异常类型

- **InvalidIPError**: IP地址格式无效或范围无效
- **IPRangeExhaustedError**: IP地址范围已完全分配
- **IPAllocatorError**: 其他分配器相关错误

### 错误处理示例

```python
try:
    allocator = IPAllocator("***********", "***********0", etcd_client, "/test")
    ip = allocator.allocate_ip()
except InvalidIPError as e:
    print(f"IP地址无效: {e}")
except IPRangeExhaustedError as e:
    print(f"IP范围耗尽: {e}")
except IPAllocatorError as e:
    print(f"分配器错误: {e}")
```

## 测试

### 运行测试

```bash
# 运行所有IP分配器测试
python3 -m pytest tests/test_ip_allocator.py -v

# 运行特定测试
python3 -m pytest tests/test_ip_allocator.py::TestIPAllocator::test_allocate_ip_from_reclaimed -v
```

### 演示程序

```bash
# 运行完整演示（需要ETCD服务器）
python3 examples/ip_allocator_demo.py

# 运行错误处理演示（不需要ETCD）
python3 examples/ip_allocator_demo.py --error-demo
```

## 性能考虑

### 内存使用

- 已分配IP使用Set数据结构，查找时间复杂度O(1)
- 回收IP使用List数据结构，支持FIFO回收策略
- 内存使用量与分配的IP数量成正比

### ETCD操作

- 每次分配/释放操作都会更新ETCD
- 使用JSON格式存储，便于调试和监控
- 支持ETCD的事务和一致性保证

### 扩展性

- 支持多个分配器实例使用不同的ETCD键
- 可以为不同的网络段创建独立的分配器
- 支持分布式环境下的并发访问

## 集成指南

### 与v-switch集成

IP分配器已集成到v-switch核心服务中，可以通过以下方式使用：

```python
from src.v_switch.core_service import IPAllocator

# 在网络服务中使用
class NetworkService:
    def __init__(self, etcd_client, config):
        self.ip_allocator = IPAllocator(
            ip_min=config.network.ip_min,
            ip_max=config.network.ip_max,
            etcd_client=etcd_client,
            key="/network/ip_allocator"
        )
    
    def allocate_subnet_ip(self):
        return self.ip_allocator.allocate_ip()
```

### 配置建议

- 为不同用途创建独立的IP分配器实例
- 使用有意义的ETCD键名便于管理
- 定期监控IP使用率，及时扩展范围
- 在生产环境中启用ETCD认证和TLS

## 故障排除

### 常见问题

1. **ETCD连接失败**: 检查ETCD服务状态和网络连接
2. **IP范围耗尽**: 扩展IP范围或清理未使用的分配
3. **数据不一致**: 检查ETCD数据完整性，必要时重新初始化
4. **性能问题**: 监控ETCD响应时间，考虑优化网络配置

### 调试技巧

- 启用DEBUG级别日志查看详细操作信息
- 使用ETCD客户端工具检查存储的数据
- 监控分配器状态和使用率变化
- 使用测试用例验证功能正确性
