# cloudlink SDN 设计v2

## 数据库表设计
使用MariaDB|MySQL作为数据库, 主键统一使用自增ID
启动时检查表创建情况, 不存在则初始化表

### ip_pool 表, 记录所有ip池信息
```
id: 主键
name: ip池名称
type: ip池类型, monnet | eipnet
description: 描述
ip_start: ip池起始ip
ip_end: ip池结束ip
vlan_id: 该ip池所属的vlan id
next_ip: 下一个分配的ip
```

### ip_pool_allocated 表, 记录ip池已分配ip信息
```
id: 主键
pool_id: 关联的ip池id
pool_type: 关联的ip池类型, monnet | eipnet
ip: 分配的ip
status: 分配状态, allocated | reclaimed
```

### network 表, 记录所有子网信息
```
id: 主键
server_type: 子网类型, guest | mixed | eip
tenant_id: 租户标识
vlan_id: 子网vlan id
ip_start: 子网起始ip
ip_end: 子网结束ip
ip_mask: 子网掩码
ip_gateway: 子网网关ip
ip6_start: 子网起始ipv6
ip6_end: 子网结束ipv6
ip6_mask: 子网掩码
ip6_gateway: 子网网关ipv6
monitor_ip: 监控网ip
monitor_gateway_ip: 监控网关ip
```


### eip_binding 表, 记录所有eip
```
id: 主键
eip: eip地址
eip_vlan_id: eip的vlan id
internal_ip: 内网ip
sub_vlan_id: 子网vlan id
rate: 保证带宽, 这是该EIP在任何情况下都能获得的最低速率。不填则默认满带宽
ceil: 最高带宽, 当有空闲带宽时，该EIP可以借用带宽，但不能超过这个上限。不填则默认满带宽
```

### eip_snat 表, 记录所有snat规则
```
id: 主键
subnet_id: 子网id
eip: eip地址
gateway_ip: 网关ip
mask: 网关掩码
```

### eip_dnat 表, 记录所有dnat规则
```
id: 主键
subnet_id: 子网id
eip: eip地址
internal_ip: 内网ip
```


### redo_log 表, 记录主节点执行的操作, 主要用于从节点同步
```
id: 主键
type: 类型, create | update | delete
object: 操作对象, network | eip_binding | eip_snat | eip_dnat
data: 数据内容, json格式
time: 时间戳
```

### node_status 表, 记录所有节点状态信息
```
id: 主键
node_id: 节点id
group_id: 节点分组标识
endpoint: 节点地址
role: 节点角色, leader | follower | candidate
status: 节点状态, running | error
last_applied: 最后应用日志索引
last_heartbeat: 最后一次心跳时间
```

## core
核心模块, 运行在网络节点上, 用于管理网络配置;

### 初始化
1. 读取配置文件
2. 连接到MariaDB数据库
3. 启动gRPC服务
4. 从配置文件获取 group_id, endpoint信息, 并将节点信息注册到raft_status表中;
5. 从数据库拉取raft_status表中所有相同group_id的节点信息, 判断是否有leader节点;
6. 如果有leader节点, 则更新raft_status表中节点角色为follower;
7. 如果没有leader节点, 则开始竞选leader;
8. 竞选成功则更新raft_status表中节点角色为leader;
9. 启动心跳线程, 并开始向其他节点发送心跳;

### raft 状态机
#### leader
1. 处理来自客户端的请求
2. 将请求写入operation_log表, 并发送给所有follower节点
3. 等待至少一个follower节点响应
4. 响应客户端的请求
#### follower
1. 定时向leader发送心跳
2. 超过一定次数没有收到leader心跳响应, 则开始竞选leader
3. 收到leader的请求后, 执行请求, 并更新commit_index
4. 如果执行失败则更新raft_status表中节点状态为error
5. 响应leader的请求
#### 竞选leader:
1. 生成一个随机的任期号
2. 给raft_status表中所有节点发送竞选请求
3. 如果收到半数以上的节点同意, 则成为leader
4. 如果收到拒绝, 则重新生成任期号, 并再次竞选
5. 如果在竞选超时时间内没有结果, 则重新生成任期号, 并再次竞选

### gRPC接口



