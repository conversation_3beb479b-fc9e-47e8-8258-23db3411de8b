"""
Raft协议数据库操作模块
"""

import json
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

try:
    import pymysql
    PYMYSQL_AVAILABLE = True
except ImportError:
    PYMYSQL_AVAILABLE = False
    pymysql = None

from .raft_pb2 import LogEntry, NodeRole, NodeStatus


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    user: str = "root"
    password: str = ""
    database: str = "cloudlink"
    charset: str = "utf8mb4"


class RaftDatabase:
    """Raft协议数据库操作类"""
    
    def __init__(self, config: DatabaseConfig):
        """初始化数据库连接
        
        Args:
            config: 数据库配置
        """
        if not PYMYSQL_AVAILABLE:
            raise ImportError("pymysql is required for database operations. Install with: pip install pymysql")
        
        self.config = config
        self.logger = logging.getLogger("raft.database")
        self._connection = None
        
        # 连接到数据库
        self._connect()
        
        # 确保表存在
        self._ensure_tables()
    
    def _connect(self) -> None:
        """连接到数据库"""
        try:
            self._connection = pymysql.connect(
                host=self.config.host,
                port=self.config.port,
                user=self.config.user,
                password=self.config.password,
                database=self.config.database,
                charset=self.config.charset,
                autocommit=True
            )
            self.logger.info(f"Connected to database {self.config.host}:{self.config.port}/{self.config.database}")
        except Exception as e:
            self.logger.error(f"Failed to connect to database: {e}")
            raise
    
    def _ensure_connection(self) -> None:
        """确保数据库连接有效"""
        try:
            if not self._connection or not self._connection.open:
                self._connect()
            else:
                # 测试连接
                self._connection.ping(reconnect=True)
        except Exception as e:
            self.logger.warning(f"Database connection lost, reconnecting: {e}")
            self._connect()
    
    def _ensure_tables(self) -> None:
        """确保必要的表存在"""
        self._ensure_connection()
        
        # 创建raft_status表
        raft_status_sql = """
        CREATE TABLE IF NOT EXISTS raft_status (
            id INT AUTO_INCREMENT PRIMARY KEY,
            node_id VARCHAR(64) NOT NULL UNIQUE,
            group_id VARCHAR(64) NOT NULL,
            endpoint VARCHAR(255) NOT NULL,
            role ENUM('leader', 'follower', 'candidate') NOT NULL DEFAULT 'follower',
            status ENUM('running', 'error') NOT NULL DEFAULT 'running',
            term BIGINT NOT NULL DEFAULT 0,
            commit_index BIGINT NOT NULL DEFAULT 0,
            last_applied BIGINT NOT NULL DEFAULT 0,
            last_heartbeat BIGINT NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_group_id (group_id),
            INDEX idx_role (role),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """
        
        # 创建operation_log表
        operation_log_sql = """
        CREATE TABLE IF NOT EXISTS operation_log (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            node_id VARCHAR(64) NOT NULL,
            log_index BIGINT NOT NULL,
            term BIGINT NOT NULL,
            type ENUM('create', 'update', 'delete') NOT NULL,
            object ENUM('network', 'eip_binding', 'eip_snat', 'eip_dnat') NOT NULL,
            data JSON NOT NULL,
            timestamp BIGINT NOT NULL,
            status ENUM('success', 'failed') NOT NULL DEFAULT 'success',
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY uk_node_index (node_id, log_index),
            INDEX idx_term (term),
            INDEX idx_type_object (type, object),
            INDEX idx_status (status),
            INDEX idx_timestamp (timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """
        
        try:
            with self._connection.cursor() as cursor:
                cursor.execute(raft_status_sql)
                cursor.execute(operation_log_sql)
            self.logger.info("Database tables ensured")
        except Exception as e:
            self.logger.error(f"Failed to create tables: {e}")
            raise
    
    def register_node(self, node_id: str, group_id: str, endpoint: str) -> bool:
        """注册节点到raft_status表"""
        self._ensure_connection()
        
        sql = """
        INSERT INTO raft_status (node_id, group_id, endpoint, role, status, term, commit_index, last_applied, last_heartbeat)
        VALUES (%s, %s, %s, 'follower', 'running', 0, 0, 0, %s)
        ON DUPLICATE KEY UPDATE
        group_id = VALUES(group_id),
        endpoint = VALUES(endpoint),
        status = 'running',
        last_heartbeat = VALUES(last_heartbeat),
        updated_at = CURRENT_TIMESTAMP
        """
        
        try:
            with self._connection.cursor() as cursor:
                cursor.execute(sql, (node_id, group_id, endpoint, int(time.time() * 1000)))
            self.logger.info(f"Registered node {node_id} in group {group_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to register node {node_id}: {e}")
            return False
    
    def update_node_status(self, node_id: str, role: str, status: str, term: int,
                          commit_index: int, last_applied: int) -> bool:
        """更新节点状态"""
        self._ensure_connection()
        
        sql = """
        UPDATE raft_status 
        SET role = %s, status = %s, term = %s, commit_index = %s, 
            last_applied = %s, last_heartbeat = %s, updated_at = CURRENT_TIMESTAMP
        WHERE node_id = %s
        """
        
        try:
            with self._connection.cursor() as cursor:
                cursor.execute(sql, (role, status, term, commit_index, last_applied,
                                   int(time.time() * 1000), node_id))
            return True
        except Exception as e:
            self.logger.error(f"Failed to update node status for {node_id}: {e}")
            return False
    
    def get_cluster_nodes(self, group_id: str) -> List[Dict[str, Any]]:
        """获取集群中的所有节点"""
        self._ensure_connection()
        
        sql = """
        SELECT node_id, group_id, endpoint, role, status, term, 
               commit_index, last_applied, last_heartbeat
        FROM raft_status 
        WHERE group_id = %s
        ORDER BY node_id
        """
        
        try:
            with self._connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, (group_id,))
                return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"Failed to get cluster nodes for group {group_id}: {e}")
            return []
    
    def get_leader_node(self, group_id: str) -> Optional[Dict[str, Any]]:
        """获取Leader节点"""
        self._ensure_connection()
        
        sql = """
        SELECT node_id, group_id, endpoint, role, status, term,
               commit_index, last_applied, last_heartbeat
        FROM raft_status 
        WHERE group_id = %s AND role = 'leader' AND status = 'running'
        ORDER BY term DESC, last_heartbeat DESC
        LIMIT 1
        """
        
        try:
            with self._connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, (group_id,))
                return cursor.fetchone()
        except Exception as e:
            self.logger.error(f"Failed to get leader node for group {group_id}: {e}")
            return None
    
    def save_log_entry(self, node_id: str, log_entry: LogEntry, status: str = "success",
                      error_message: str = "") -> bool:
        """保存日志条目到operation_log表"""
        self._ensure_connection()
        
        sql = """
        INSERT INTO operation_log (node_id, log_index, term, type, object, data, 
                                 timestamp, status, error_message)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        term = VALUES(term),
        type = VALUES(type),
        object = VALUES(object),
        data = VALUES(data),
        timestamp = VALUES(timestamp),
        status = VALUES(status),
        error_message = VALUES(error_message)
        """
        
        try:
            with self._connection.cursor() as cursor:
                cursor.execute(sql, (
                    node_id, log_entry.index, log_entry.term, log_entry.type,
                    log_entry.object, log_entry.data, log_entry.timestamp,
                    status, error_message
                ))
            return True
        except Exception as e:
            self.logger.error(f"Failed to save log entry {log_entry.index} for node {node_id}: {e}")
            return False
    
    def get_log_entries(self, node_id: str, start_index: int = 1, limit: int = 100) -> List[Dict[str, Any]]:
        """获取日志条目"""
        self._ensure_connection()
        
        sql = """
        SELECT node_id, log_index, term, type, object, data, timestamp, status, error_message
        FROM operation_log 
        WHERE node_id = %s AND log_index >= %s
        ORDER BY log_index
        LIMIT %s
        """
        
        try:
            with self._connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, (node_id, start_index, limit))
                return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"Failed to get log entries for node {node_id}: {e}")
            return []
    
    def get_last_log_index(self, node_id: str) -> int:
        """获取最后一个日志索引"""
        self._ensure_connection()
        
        sql = "SELECT MAX(log_index) as max_index FROM operation_log WHERE node_id = %s"
        
        try:
            with self._connection.cursor() as cursor:
                cursor.execute(sql, (node_id,))
                result = cursor.fetchone()
                return result[0] if result[0] is not None else 0
        except Exception as e:
            self.logger.error(f"Failed to get last log index for node {node_id}: {e}")
            return 0
    
    def cleanup_old_logs(self, node_id: str, keep_count: int = 1000) -> bool:
        """清理旧的日志条目"""
        self._ensure_connection()
        
        sql = """
        DELETE FROM operation_log 
        WHERE node_id = %s AND log_index < (
            SELECT max_index - %s FROM (
                SELECT MAX(log_index) as max_index FROM operation_log WHERE node_id = %s
            ) t
        )
        """
        
        try:
            with self._connection.cursor() as cursor:
                cursor.execute(sql, (node_id, keep_count, node_id))
                deleted_count = cursor.rowcount
                if deleted_count > 0:
                    self.logger.info(f"Cleaned up {deleted_count} old log entries for node {node_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to cleanup old logs for node {node_id}: {e}")
            return False
    
    def close(self) -> None:
        """关闭数据库连接"""
        if self._connection:
            try:
                self._connection.close()
                self.logger.info("Database connection closed")
            except Exception as e:
                self.logger.error(f"Error closing database connection: {e}")
            finally:
                self._connection = None
