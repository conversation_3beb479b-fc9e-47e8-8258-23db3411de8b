#!/usr/bin/env python3
"""
Raft gRPC服务端演示程序
"""

import os
import sys
import time
import signal
import logging
import argparse
import threading
from concurrent import futures

import grpc

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from v_switch.grpc.raft_engine import RaftEngine
from v_switch.grpc.raft_node import RaftNodeConfig
from v_switch.grpc.raft_pb2_grpc import add_RaftServiceServicer_to_server
from v_switch.grpc.database import RaftDatabase, DatabaseConfig


class RaftDemoServer:
    """Raft演示服务器"""
    
    def __init__(self, node_id: str, port: int, cluster_config: dict, db_config: dict = None):
        """初始化演示服务器
        
        Args:
            node_id: 节点ID
            port: 服务端口
            cluster_config: 集群配置 {node_id: endpoint}
            db_config: 数据库配置
        """
        self.node_id = node_id
        self.port = port
        self.cluster_config = cluster_config
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(f"demo.server.{node_id}")
        
        # 初始化数据库（如果配置了）
        self.database = None
        if db_config:
            try:
                self.database = RaftDatabase(DatabaseConfig(**db_config))
                self.logger.info("Database initialized")
            except Exception as e:
                self.logger.warning(f"Failed to initialize database: {e}")
        
        # 创建Raft引擎
        config = RaftNodeConfig(
            node_id=node_id,
            group_id="demo-cluster",
            endpoint=f"localhost:{port}",
            election_timeout_min=5.0,
            election_timeout_max=10.0,
            heartbeat_interval=2.0,
            request_timeout=3.0
        )
        
        self.raft_engine = RaftEngine(
            config=config,
            cluster_endpoints=cluster_config,
            log_apply_callback=self._apply_log
        )
        
        # gRPC服务器
        self.grpc_server = None
        self._running = False
        
        # 注册节点到数据库
        if self.database:
            self.database.register_node(node_id, "demo-cluster", f"localhost:{port}")
    
    def _apply_log(self, log_entry) -> bool:
        """应用日志条目的回调函数"""
        try:
            self.logger.info(f"Applying log entry {log_entry.index}: {log_entry.type} {log_entry.object}")
            
            # 保存到数据库
            if self.database:
                success = self.database.save_log_entry(self.node_id, log_entry)
                if not success:
                    self.logger.error(f"Failed to save log entry {log_entry.index} to database")
                    return False
            
            # 这里可以添加实际的业务逻辑处理
            # 例如：创建网络、绑定EIP等
            
            return True
        except Exception as e:
            self.logger.error(f"Error applying log entry {log_entry.index}: {e}")
            return False
    
    def start(self):
        """启动服务器"""
        if self._running:
            self.logger.warning("Server is already running")
            return
        
        try:
            # 创建gRPC服务器
            self.grpc_server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
            
            # 添加Raft服务
            add_RaftServiceServicer_to_server(self.raft_engine.service, self.grpc_server)
            
            # 监听端口
            listen_addr = f'[::]:{self.port}'
            self.grpc_server.add_insecure_port(listen_addr)
            
            # 启动gRPC服务器
            self.grpc_server.start()
            self.logger.info(f"gRPC server started on {listen_addr}")
            
            # 启动Raft引擎
            self.raft_engine.start()
            
            self._running = True
            
            # 启动状态更新线程
            status_thread = threading.Thread(target=self._status_update_loop, daemon=True)
            status_thread.start()
            
            self.logger.info(f"Raft demo server {self.node_id} started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start server: {e}")
            self.stop()
            raise
    
    def stop(self):
        """停止服务器"""
        if not self._running:
            return
        
        self._running = False
        
        # 停止Raft引擎
        if self.raft_engine:
            self.raft_engine.stop()
        
        # 停止gRPC服务器
        if self.grpc_server:
            self.grpc_server.stop(grace=5.0)
        
        # 关闭数据库连接
        if self.database:
            self.database.close()
        
        self.logger.info(f"Raft demo server {self.node_id} stopped")
    
    def _status_update_loop(self):
        """状态更新循环"""
        while self._running:
            try:
                if self.database:
                    # 更新节点状态到数据库
                    status = self.raft_engine.get_status()
                    self.database.update_node_status(
                        node_id=self.node_id,
                        role=status['role'],
                        status='running' if status['running'] else 'error',
                        term=status['term'],
                        commit_index=status['commit_index'],
                        last_applied=status['last_applied']
                    )
                
                time.sleep(5.0)  # 每5秒更新一次
                
            except Exception as e:
                self.logger.error(f"Error in status update loop: {e}")
                time.sleep(10.0)
    
    def wait_for_termination(self):
        """等待服务器终止"""
        try:
            while self._running:
                time.sleep(1.0)
        except KeyboardInterrupt:
            self.logger.info("Received interrupt signal")
            self.stop()
    
    def add_log_entry(self, log_type: str, obj: str, data: str) -> bool:
        """添加日志条目（仅用于测试）"""
        return self.raft_engine.append_log(log_type, obj, data)
    
    def get_status(self) -> dict:
        """获取服务器状态"""
        return self.raft_engine.get_status()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Raft gRPC Demo Server')
    parser.add_argument('--node-id', required=True, help='Node ID')
    parser.add_argument('--port', type=int, required=True, help='Server port')
    parser.add_argument('--cluster', required=True, help='Cluster config (node1:port1,node2:port2,...)')
    parser.add_argument('--db-host', default='localhost', help='Database host')
    parser.add_argument('--db-port', type=int, default=3306, help='Database port')
    parser.add_argument('--db-user', default='root', help='Database user')
    parser.add_argument('--db-password', default='', help='Database password')
    parser.add_argument('--db-name', default='cloudlink', help='Database name')
    parser.add_argument('--no-db', action='store_true', help='Disable database')
    
    args = parser.parse_args()
    
    # 解析集群配置
    cluster_config = {}
    for node_endpoint in args.cluster.split(','):
        if ':' in node_endpoint:
            node, port = node_endpoint.split(':', 1)
            cluster_config[node] = f"localhost:{port}"
    
    # 数据库配置
    db_config = None
    if not args.no_db:
        db_config = {
            'host': args.db_host,
            'port': args.db_port,
            'user': args.db_user,
            'password': args.db_password,
            'database': args.db_name
        }
    
    # 创建服务器
    server = RaftDemoServer(args.node_id, args.port, cluster_config, db_config)
    
    # 设置信号处理
    def signal_handler(signum, frame):
        print(f"\nReceived signal {signum}, shutting down...")
        server.stop()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动服务器
        server.start()
        
        # 等待终止
        server.wait_for_termination()
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
