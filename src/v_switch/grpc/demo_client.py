#!/usr/bin/env python3
"""
Raft gRPC客户端演示程序
"""

import os
import sys
import time
import json
import argparse
import logging

import grpc

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from v_switch.grpc.raft_pb2 import (
    GetNodeStatusRequest, RequestVoteRequest, AppendEntriesRequest, LogEntry
)
from v_switch.grpc.raft_pb2_grpc import RaftServiceStub


class RaftDemoClient:
    """Raft演示客户端"""
    
    def __init__(self, endpoints: list, timeout: float = 5.0):
        """初始化客户端
        
        Args:
            endpoints: 服务端点列表
            timeout: 请求超时时间
        """
        self.endpoints = endpoints
        self.timeout = timeout
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger("demo.client")
        
        # gRPC连接
        self.channels = {}
        self.stubs = {}
        
        # 初始化连接
        self._init_connections()
    
    def _init_connections(self):
        """初始化gRPC连接"""
        for endpoint in self.endpoints:
            try:
                channel = grpc.insecure_channel(endpoint)
                stub = RaftServiceStub(channel)
                
                self.channels[endpoint] = channel
                self.stubs[endpoint] = stub
                
                self.logger.info(f"Connected to {endpoint}")
            except Exception as e:
                self.logger.error(f"Failed to connect to {endpoint}: {e}")
    
    def close_connections(self):
        """关闭所有连接"""
        for endpoint, channel in self.channels.items():
            try:
                channel.close()
                self.logger.info(f"Closed connection to {endpoint}")
            except Exception as e:
                self.logger.error(f"Error closing connection to {endpoint}: {e}")
        
        self.channels.clear()
        self.stubs.clear()
    
    def get_node_status(self, endpoint: str) -> dict:
        """获取节点状态"""
        if endpoint not in self.stubs:
            self.logger.error(f"No connection to {endpoint}")
            return {}
        
        try:
            request = GetNodeStatusRequest(node_id="client")
            response = self.stubs[endpoint].GetNodeStatus(request, timeout=self.timeout)
            
            return {
                'node_id': response.node_id,
                'group_id': response.group_id,
                'endpoint': response.endpoint,
                'role': response.role,
                'status': response.status,
                'term': response.term,
                'commit_index': response.commit_index,
                'last_applied': response.last_applied,
                'last_heartbeat': response.last_heartbeat,
                'leader_id': response.leader_id
            }
        except grpc.RpcError as e:
            self.logger.error(f"gRPC error getting status from {endpoint}: {e.code()}")
            return {}
        except Exception as e:
            self.logger.error(f"Error getting status from {endpoint}: {e}")
            return {}
    
    def get_all_status(self) -> dict:
        """获取所有节点状态"""
        results = {}
        for endpoint in self.endpoints:
            status = self.get_node_status(endpoint)
            if status:
                results[endpoint] = status
        return results
    
    def find_leader(self) -> tuple:
        """查找Leader节点"""
        for endpoint in self.endpoints:
            status = self.get_node_status(endpoint)
            if status and status.get('role') == 1:  # LEADER = 1
                return endpoint, status
        return None, None
    
    def send_test_log(self, endpoint: str, log_type: str = "create", 
                     obj: str = "network", data: dict = None) -> bool:
        """发送测试日志条目（仅用于演示，实际应该通过Leader处理）"""
        if endpoint not in self.stubs:
            self.logger.error(f"No connection to {endpoint}")
            return False
        
        if data is None:
            data = {"test": "data", "timestamp": int(time.time())}
        
        try:
            # 创建日志条目
            log_entry = LogEntry(
                term=1,  # 简化处理
                index=1,  # 简化处理
                type=log_type,
                object=obj,
                data=json.dumps(data),
                timestamp=int(time.time() * 1000)
            )
            
            # 发送AppendEntries请求
            request = AppendEntriesRequest(
                term=1,
                leader_id="test-client",
                prev_log_index=0,
                prev_log_term=0,
                entries=[log_entry],
                leader_commit=0
            )
            
            response = self.stubs[endpoint].AppendEntries(request, timeout=self.timeout)
            
            self.logger.info(f"Sent test log to {endpoint}: success={response.success}")
            return response.success
            
        except grpc.RpcError as e:
            self.logger.error(f"gRPC error sending test log to {endpoint}: {e.code()}")
            return False
        except Exception as e:
            self.logger.error(f"Error sending test log to {endpoint}: {e}")
            return False
    
    def monitor_cluster(self, interval: float = 5.0, duration: float = 60.0):
        """监控集群状态"""
        self.logger.info(f"Starting cluster monitoring for {duration} seconds...")
        
        start_time = time.time()
        while time.time() - start_time < duration:
            try:
                print("\n" + "="*80)
                print(f"Cluster Status at {time.strftime('%Y-%m-%d %H:%M:%S')}")
                print("="*80)
                
                all_status = self.get_all_status()
                
                if not all_status:
                    print("No nodes responding")
                else:
                    # 显示节点状态
                    for endpoint, status in all_status.items():
                        role_name = {0: "FOLLOWER", 1: "LEADER", 2: "CANDIDATE"}.get(status.get('role'), "UNKNOWN")
                        status_name = {0: "RUNNING", 1: "ERROR", 2: "STOPPED"}.get(status.get('status'), "UNKNOWN")
                        
                        print(f"Node: {status.get('node_id', 'unknown')} ({endpoint})")
                        print(f"  Role: {role_name}")
                        print(f"  Status: {status_name}")
                        print(f"  Term: {status.get('term', 0)}")
                        print(f"  Commit Index: {status.get('commit_index', 0)}")
                        print(f"  Last Applied: {status.get('last_applied', 0)}")
                        print(f"  Leader: {status.get('leader_id', 'none')}")
                        print()
                    
                    # 查找Leader
                    leader_endpoint, leader_status = self.find_leader()
                    if leader_endpoint:
                        print(f"Current Leader: {leader_status.get('node_id')} ({leader_endpoint})")
                    else:
                        print("No leader found")
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                print("\nMonitoring interrupted")
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring: {e}")
                time.sleep(interval)
        
        self.logger.info("Cluster monitoring completed")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Raft gRPC Demo Client')
    parser.add_argument('--endpoints', required=True, 
                       help='Server endpoints (host1:port1,host2:port2,...)')
    parser.add_argument('--timeout', type=float, default=5.0, help='Request timeout')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # status命令
    status_parser = subparsers.add_parser('status', help='Get cluster status')
    
    # monitor命令
    monitor_parser = subparsers.add_parser('monitor', help='Monitor cluster')
    monitor_parser.add_argument('--interval', type=float, default=5.0, help='Monitor interval')
    monitor_parser.add_argument('--duration', type=float, default=60.0, help='Monitor duration')
    
    # test-log命令
    test_log_parser = subparsers.add_parser('test-log', help='Send test log entry')
    test_log_parser.add_argument('--endpoint', required=True, help='Target endpoint')
    test_log_parser.add_argument('--type', default='create', help='Log type')
    test_log_parser.add_argument('--object', default='network', help='Log object')
    test_log_parser.add_argument('--data', help='Log data (JSON)')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 解析端点
    endpoints = []
    for endpoint in args.endpoints.split(','):
        endpoint = endpoint.strip()
        if endpoint:
            if not endpoint.startswith('localhost:') and ':' not in endpoint:
                endpoint = f"localhost:{endpoint}"
            endpoints.append(endpoint)
    
    if not endpoints:
        print("Error: No valid endpoints provided")
        return
    
    # 创建客户端
    client = RaftDemoClient(endpoints, args.timeout)
    
    try:
        if args.command == 'status':
            # 获取集群状态
            all_status = client.get_all_status()
            print(json.dumps(all_status, indent=2))
            
        elif args.command == 'monitor':
            # 监控集群
            client.monitor_cluster(args.interval, args.duration)
            
        elif args.command == 'test-log':
            # 发送测试日志
            data = None
            if args.data:
                try:
                    data = json.loads(args.data)
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON data: {e}")
                    return
            
            success = client.send_test_log(args.endpoint, args.type, args.object, data)
            print(f"Test log sent: {'success' if success else 'failed'}")
    
    finally:
        client.close_connections()


if __name__ == '__main__':
    main()
