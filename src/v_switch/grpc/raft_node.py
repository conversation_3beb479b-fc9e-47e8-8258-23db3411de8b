"""
Raft节点状态管理模块
"""

import time
import random
import logging
import threading
from enum import Enum
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, field

from .raft_pb2 import NodeRole, NodeStatus, LogEntry


class RaftState(Enum):
    """Raft节点状态"""
    FOLLOWER = "follower"
    CANDIDATE = "candidate" 
    LEADER = "leader"


@dataclass
class RaftNodeConfig:
    """Raft节点配置"""
    node_id: str
    group_id: str
    endpoint: str
    election_timeout_min: float = 5.0  # 选举超时最小值(秒)
    election_timeout_max: float = 10.0  # 选举超时最大值(秒)
    heartbeat_interval: float = 2.0     # 心跳间隔(秒)
    request_timeout: float = 3.0        # 请求超时(秒)


@dataclass
class PersistentState:
    """需要持久化的状态"""
    current_term: int = 0               # 当前任期
    voted_for: Optional[str] = None     # 当前任期投票给了谁
    log: List[LogEntry] = field(default_factory=list)  # 日志条目


@dataclass
class VolatileState:
    """易失性状态"""
    commit_index: int = 0               # 已提交的最高日志索引
    last_applied: int = 0               # 已应用到状态机的最高日志索引


@dataclass
class LeaderState:
    """Leader特有的易失性状态"""
    next_index: Dict[str, int] = field(default_factory=dict)    # 发送给每个节点的下一个日志索引
    match_index: Dict[str, int] = field(default_factory=dict)   # 已复制到每个节点的最高日志索引


class RaftNode:
    """Raft节点状态管理器"""
    
    def __init__(self, config: RaftNodeConfig):
        """初始化Raft节点
        
        Args:
            config: 节点配置
        """
        self.config = config
        self.logger = logging.getLogger(f"raft.{config.node_id}")
        
        # 状态管理
        self.state = RaftState.FOLLOWER
        self.status = NodeStatus.RUNNING
        
        # 持久化状态
        self.persistent = PersistentState()
        
        # 易失性状态
        self.volatile = VolatileState()
        
        # Leader状态
        self.leader_state = LeaderState()
        
        # 集群信息
        self.cluster_nodes: Set[str] = set()  # 集群中的其他节点ID
        self.current_leader: Optional[str] = None
        
        # 时间管理
        self.last_heartbeat = time.time()
        self.election_deadline = self._reset_election_timeout()
        
        # 线程同步
        self._lock = threading.RLock()
        
        self.logger.info(f"Raft node {config.node_id} initialized")
    
    def _reset_election_timeout(self) -> float:
        """重置选举超时时间"""
        timeout = random.uniform(
            self.config.election_timeout_min,
            self.config.election_timeout_max
        )
        return time.time() + timeout
    
    def get_current_term(self) -> int:
        """获取当前任期"""
        with self._lock:
            return self.persistent.current_term
    
    def get_state(self) -> RaftState:
        """获取当前状态"""
        with self._lock:
            return self.state
    
    def get_leader_id(self) -> Optional[str]:
        """获取当前Leader ID"""
        with self._lock:
            return self.current_leader
    
    def is_leader(self) -> bool:
        """判断是否为Leader"""
        with self._lock:
            return self.state == RaftState.LEADER
    
    def is_follower(self) -> bool:
        """判断是否为Follower"""
        with self._lock:
            return self.state == RaftState.FOLLOWER
    
    def is_candidate(self) -> bool:
        """判断是否为Candidate"""
        with self._lock:
            return self.state == RaftState.CANDIDATE
    
    def update_cluster_nodes(self, nodes: Set[str]) -> None:
        """更新集群节点信息"""
        with self._lock:
            self.cluster_nodes = nodes.copy()
            if self.config.node_id in self.cluster_nodes:
                self.cluster_nodes.remove(self.config.node_id)
            
            # 如果是Leader，初始化新节点的索引
            if self.state == RaftState.LEADER:
                next_index = len(self.persistent.log)
                for node_id in self.cluster_nodes:
                    if node_id not in self.leader_state.next_index:
                        self.leader_state.next_index[node_id] = next_index
                        self.leader_state.match_index[node_id] = 0
    
    def become_follower(self, term: int, leader_id: Optional[str] = None) -> None:
        """转换为Follower状态"""
        with self._lock:
            if term > self.persistent.current_term:
                self.persistent.current_term = term
                self.persistent.voted_for = None
            
            self.state = RaftState.FOLLOWER
            self.current_leader = leader_id
            self.last_heartbeat = time.time()
            self.election_deadline = self._reset_election_timeout()
            
            self.logger.info(f"Became follower for term {term}, leader: {leader_id}")
    
    def become_candidate(self) -> None:
        """转换为Candidate状态"""
        with self._lock:
            self.state = RaftState.CANDIDATE
            self.persistent.current_term += 1
            self.persistent.voted_for = self.config.node_id
            self.current_leader = None
            self.election_deadline = self._reset_election_timeout()
            
            self.logger.info(f"Became candidate for term {self.persistent.current_term}")
    
    def become_leader(self) -> None:
        """转换为Leader状态"""
        with self._lock:
            self.state = RaftState.LEADER
            self.current_leader = self.config.node_id
            
            # 初始化Leader状态
            next_index = len(self.persistent.log)
            self.leader_state.next_index.clear()
            self.leader_state.match_index.clear()
            
            for node_id in self.cluster_nodes:
                self.leader_state.next_index[node_id] = next_index
                self.leader_state.match_index[node_id] = 0
            
            self.logger.info(f"Became leader for term {self.persistent.current_term}")
    
    def should_start_election(self) -> bool:
        """判断是否应该开始选举"""
        with self._lock:
            if self.state != RaftState.FOLLOWER:
                return False
            return time.time() > self.election_deadline
    
    def update_heartbeat(self) -> None:
        """更新心跳时间"""
        with self._lock:
            self.last_heartbeat = time.time()
            if self.state == RaftState.CANDIDATE:
                # 如果是Candidate状态，收到心跳说明有新Leader，转为Follower
                self.state = RaftState.FOLLOWER
                self.election_deadline = self._reset_election_timeout()
    
    def get_last_log_info(self) -> tuple[int, int]:
        """获取最后一个日志条目的索引和任期"""
        with self._lock:
            if not self.persistent.log:
                return 0, 0
            last_log = self.persistent.log[-1]
            return last_log.index, last_log.term
    
    def append_log_entry(self, entry: LogEntry) -> None:
        """添加日志条目"""
        with self._lock:
            self.persistent.log.append(entry)
    
    def get_log_entries(self, start_index: int) -> List[LogEntry]:
        """获取从指定索引开始的日志条目"""
        with self._lock:
            if start_index <= 0 or start_index > len(self.persistent.log):
                return []
            return self.persistent.log[start_index-1:]
    
    def get_node_status_info(self) -> dict:
        """获取节点状态信息"""
        with self._lock:
            return {
                'node_id': self.config.node_id,
                'group_id': self.config.group_id,
                'endpoint': self.config.endpoint,
                'role': self.state.value,
                'status': self.status,
                'term': self.persistent.current_term,
                'commit_index': self.volatile.commit_index,
                'last_applied': self.volatile.last_applied,
                'last_heartbeat': int(self.last_heartbeat * 1000),  # 转换为毫秒
                'leader_id': self.current_leader or '',
                'cluster_size': len(self.cluster_nodes) + 1,  # +1 包括自己
            }
