#!/bin/bash

# Raft集群启动脚本

set -e

# 默认配置
DEFAULT_NODES=3
DEFAULT_BASE_PORT=50051
DEFAULT_DB_HOST="localhost"
DEFAULT_DB_PORT=3306
DEFAULT_DB_USER="root"
DEFAULT_DB_PASSWORD=""
DEFAULT_DB_NAME="cloudlink"

# 解析命令行参数
NODES=${1:-$DEFAULT_NODES}
BASE_PORT=${2:-$DEFAULT_BASE_PORT}
DB_HOST=${3:-$DEFAULT_DB_HOST}
DB_PORT=${4:-$DEFAULT_DB_PORT}
DB_USER=${5:-$DEFAULT_DB_USER}
DB_PASSWORD=${6:-$DEFAULT_DB_PASSWORD}
DB_NAME=${7:-$DEFAULT_DB_NAME}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "Checking dependencies..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 is required but not installed"
        exit 1
    fi
    
    # 检查pip包
    if ! python3 -c "import grpc" &> /dev/null; then
        log_error "grpcio is required. Install with: pip install grpcio grpcio-tools"
        exit 1
    fi
    
    log_info "Dependencies check passed"
}

# 生成集群配置
generate_cluster_config() {
    local nodes=$1
    local base_port=$2
    local config=""
    
    for ((i=1; i<=nodes; i++)); do
        local port=$((base_port + i - 1))
        if [ $i -gt 1 ]; then
            config="${config},"
        fi
        config="${config}node${i}:${port}"
    done
    
    echo "$config"
}

# 检查端口是否可用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1
    else
        return 0
    fi
}

# 启动单个节点
start_node() {
    local node_id=$1
    local port=$2
    local cluster_config=$3
    local db_args=$4
    
    log_info "Starting node $node_id on port $port..."
    
    # 检查端口
    if ! check_port $port; then
        log_error "Port $port is already in use"
        return 1
    fi
    
    # 构建命令
    local cmd="python3 demo_server.py --node-id $node_id --port $port --cluster $cluster_config"
    
    if [ -n "$db_args" ]; then
        cmd="$cmd $db_args"
    else
        cmd="$cmd --no-db"
    fi
    
    # 启动节点（后台运行）
    nohup $cmd > "logs/node_${node_id}.log" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > "pids/node_${node_id}.pid"
    
    log_info "Node $node_id started with PID $pid"
    return 0
}

# 停止所有节点
stop_cluster() {
    log_info "Stopping cluster..."
    
    if [ -d "pids" ]; then
        for pid_file in pids/node_*.pid; do
            if [ -f "$pid_file" ]; then
                local pid=$(cat "$pid_file")
                local node_id=$(basename "$pid_file" .pid)
                
                if kill -0 $pid 2>/dev/null; then
                    log_info "Stopping $node_id (PID: $pid)..."
                    kill $pid
                    
                    # 等待进程结束
                    local count=0
                    while kill -0 $pid 2>/dev/null && [ $count -lt 10 ]; do
                        sleep 1
                        count=$((count + 1))
                    done
                    
                    # 强制杀死
                    if kill -0 $pid 2>/dev/null; then
                        log_warn "Force killing $node_id..."
                        kill -9 $pid
                    fi
                fi
                
                rm -f "$pid_file"
            fi
        done
        
        rmdir pids 2>/dev/null || true
    fi
    
    log_info "Cluster stopped"
}

# 检查集群状态
check_cluster_status() {
    log_info "Checking cluster status..."
    
    if [ ! -d "pids" ]; then
        log_info "No cluster is running"
        return
    fi
    
    local running_nodes=0
    local total_nodes=0
    
    for pid_file in pids/node_*.pid; do
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            local node_id=$(basename "$pid_file" .pid)
            total_nodes=$((total_nodes + 1))
            
            if kill -0 $pid 2>/dev/null; then
                log_info "$node_id is running (PID: $pid)"
                running_nodes=$((running_nodes + 1))
            else
                log_warn "$node_id is not running (stale PID: $pid)"
                rm -f "$pid_file"
            fi
        fi
    done
    
    log_info "Cluster status: $running_nodes/$total_nodes nodes running"
}

# 显示帮助信息
show_help() {
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  start [nodes] [base_port] [db_host] [db_port] [db_user] [db_password] [db_name]"
    echo "    Start a Raft cluster with specified number of nodes"
    echo "    Default: 3 nodes starting from port 50051"
    echo ""
    echo "  stop"
    echo "    Stop the running cluster"
    echo ""
    echo "  status"
    echo "    Check cluster status"
    echo ""
    echo "  restart [nodes] [base_port] [db_host] [db_port] [db_user] [db_password] [db_name]"
    echo "    Restart the cluster"
    echo ""
    echo "  logs [node_id]"
    echo "    Show logs for a specific node or all nodes"
    echo ""
    echo "  monitor [endpoints]"
    echo "    Monitor cluster using demo client"
    echo ""
    echo "Examples:"
    echo "  $0 start                          # Start 3-node cluster"
    echo "  $0 start 5 60001                 # Start 5-node cluster from port 60001"
    echo "  $0 stop                          # Stop cluster"
    echo "  $0 status                        # Check status"
    echo "  $0 logs node1                    # Show node1 logs"
    echo "  $0 monitor 50051,50052,50053     # Monitor cluster"
}

# 显示日志
show_logs() {
    local node_id=$1
    
    if [ -n "$node_id" ]; then
        local log_file="logs/${node_id}.log"
        if [ -f "$log_file" ]; then
            log_info "Showing logs for $node_id:"
            tail -f "$log_file"
        else
            log_error "Log file not found: $log_file"
        fi
    else
        log_info "Showing logs for all nodes:"
        if [ -d "logs" ]; then
            for log_file in logs/node_*.log; do
                if [ -f "$log_file" ]; then
                    local node_id=$(basename "$log_file" .log)
                    echo -e "\n${BLUE}=== $node_id ===${NC}"
                    tail -20 "$log_file"
                fi
            done
        else
            log_info "No log files found"
        fi
    fi
}

# 监控集群
monitor_cluster() {
    local endpoints=$1
    
    if [ -z "$endpoints" ]; then
        # 自动生成端点列表
        local ports=""
        for ((i=1; i<=NODES; i++)); do
            local port=$((BASE_PORT + i - 1))
            if [ $i -gt 1 ]; then
                ports="${ports},"
            fi
            ports="${ports}${port}"
        done
        endpoints=$ports
    fi
    
    log_info "Monitoring cluster endpoints: $endpoints"
    python3 demo_client.py --endpoints "$endpoints" monitor --duration 300
}

# 主函数
main() {
    local command=${1:-"help"}
    
    case $command in
        "start")
            shift
            NODES=${1:-$DEFAULT_NODES}
            BASE_PORT=${2:-$DEFAULT_BASE_PORT}
            DB_HOST=${3:-$DEFAULT_DB_HOST}
            DB_PORT=${4:-$DEFAULT_DB_PORT}
            DB_USER=${5:-$DEFAULT_DB_USER}
            DB_PASSWORD=${6:-$DEFAULT_DB_PASSWORD}
            DB_NAME=${7:-$DEFAULT_DB_NAME}
            
            check_dependencies
            
            # 创建必要的目录
            mkdir -p logs pids
            
            # 生成集群配置
            local cluster_config=$(generate_cluster_config $NODES $BASE_PORT)
            log_info "Cluster config: $cluster_config"
            
            # 构建数据库参数
            local db_args=""
            if [ -n "$DB_HOST" ] && [ "$DB_HOST" != "none" ]; then
                db_args="--db-host $DB_HOST --db-port $DB_PORT --db-user $DB_USER --db-name $DB_NAME"
                if [ -n "$DB_PASSWORD" ]; then
                    db_args="$db_args --db-password $DB_PASSWORD"
                fi
            fi
            
            # 启动所有节点
            for ((i=1; i<=NODES; i++)); do
                local port=$((BASE_PORT + i - 1))
                start_node "node$i" $port "$cluster_config" "$db_args"
                sleep 1  # 避免同时启动导致的竞争
            done
            
            log_info "Cluster started with $NODES nodes"
            log_info "Use '$0 status' to check cluster status"
            log_info "Use '$0 monitor' to monitor cluster"
            ;;
            
        "stop")
            stop_cluster
            ;;
            
        "status")
            check_cluster_status
            ;;
            
        "restart")
            shift
            stop_cluster
            sleep 2
            main start "$@"
            ;;
            
        "logs")
            show_logs $2
            ;;
            
        "monitor")
            monitor_cluster $2
            ;;
            
        "help"|"-h"|"--help")
            show_help
            ;;
            
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
