"""
Raft协议核心引擎
"""

import time
import threading
import logging
from typing import Dict, List, Optional, Callable, Set
from concurrent.futures import ThreadPoolExecutor

from .raft_pb2 import LogEntry
from .raft_node import RaftNode, RaftNodeConfig, RaftState
from .raft_client import RaftClient
from .raft_server import RaftServiceImpl


class RaftEngine:
    """Raft协议核心引擎"""
    
    def __init__(self, config: RaftNodeConfig, 
                 cluster_endpoints: Dict[str, str],
                 log_apply_callback: Optional[Callable[[LogEntry], bool]] = None):
        """初始化Raft引擎
        
        Args:
            config: 节点配置
            cluster_endpoints: 集群节点端点映射 {node_id: endpoint}
            log_apply_callback: 日志应用回调函数
        """
        self.config = config
        self.cluster_endpoints = cluster_endpoints.copy()
        self.log_apply_callback = log_apply_callback
        
        # 移除自己的端点
        if config.node_id in self.cluster_endpoints:
            del self.cluster_endpoints[config.node_id]
        
        self.logger = logging.getLogger(f"raft.engine.{config.node_id}")
        
        # 初始化组件
        self.node = RaftNode(config)
        self.client = RaftClient(config.node_id, config.request_timeout)
        self.service = RaftServiceImpl(self.node)
        
        # 更新集群节点信息
        self.node.update_cluster_nodes(set(self.cluster_endpoints.keys()))
        
        # 线程管理
        self._running = False
        self._threads: List[threading.Thread] = []
        self._executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix=f"raft-{config.node_id}")
        
        self.logger.info(f"Raft engine initialized for node {config.node_id}")
    
    def start(self) -> None:
        """启动Raft引擎"""
        if self._running:
            self.logger.warning("Raft engine is already running")
            return
        
        self._running = True
        
        # 启动主循环线程
        main_thread = threading.Thread(
            target=self._main_loop,
            name=f"raft-main-{self.config.node_id}",
            daemon=True
        )
        main_thread.start()
        self._threads.append(main_thread)
        
        # 启动日志应用线程
        apply_thread = threading.Thread(
            target=self._apply_logs_loop,
            name=f"raft-apply-{self.config.node_id}",
            daemon=True
        )
        apply_thread.start()
        self._threads.append(apply_thread)
        
        self.logger.info("Raft engine started")
    
    def stop(self) -> None:
        """停止Raft引擎"""
        if not self._running:
            return
        
        self._running = False
        
        # 等待线程结束
        for thread in self._threads:
            if thread.is_alive():
                thread.join(timeout=5.0)
        
        # 关闭客户端连接
        self.client.close_connections()
        
        # 关闭线程池
        self._executor.shutdown(wait=True)
        
        self.logger.info("Raft engine stopped")
    
    def _main_loop(self) -> None:
        """主循环"""
        while self._running:
            try:
                if self.node.is_leader():
                    self._leader_loop()
                elif self.node.is_candidate():
                    self._candidate_loop()
                else:  # Follower
                    self._follower_loop()
                
                time.sleep(0.1)  # 短暂休眠避免CPU占用过高
                
            except Exception as e:
                self.logger.error(f"Error in main loop: {e}")
                time.sleep(1.0)
    
    def _leader_loop(self) -> None:
        """Leader主循环"""
        # 发送心跳
        self._send_heartbeats()
        
        # 检查是否需要复制日志
        self._replicate_logs()
        
        # 更新commit_index
        self._update_commit_index()
        
        time.sleep(self.config.heartbeat_interval)
    
    def _candidate_loop(self) -> None:
        """Candidate主循环"""
        # 开始选举
        if self._start_election():
            self.node.become_leader()
            self.logger.info("Won election, became leader")
        else:
            # 选举失败，等待下次选举或转为Follower
            time.sleep(0.5)
    
    def _follower_loop(self) -> None:
        """Follower主循环"""
        # 检查是否需要开始选举
        if self.node.should_start_election():
            self.node.become_candidate()
            self.logger.info("Election timeout, became candidate")
        
        time.sleep(0.5)
    
    def _send_heartbeats(self) -> None:
        """发送心跳到所有Follower"""
        if not self.cluster_endpoints:
            return
        
        term = self.node.get_current_term()
        leader_id = self.config.node_id
        leader_commit = self.node.volatile.commit_index
        
        # 为每个节点发送心跳
        for node_id, endpoint in self.cluster_endpoints.items():
            self._executor.submit(self._send_heartbeat_to_node, node_id, endpoint, term, leader_id, leader_commit)
    
    def _send_heartbeat_to_node(self, node_id: str, endpoint: str, term: int, leader_id: str, leader_commit: int) -> None:
        """向单个节点发送心跳"""
        try:
            # 获取该节点的下一个日志索引
            next_index = self.node.leader_state.next_index.get(node_id, len(self.node.persistent.log))
            prev_log_index = next_index - 1
            prev_log_term = 0
            
            if prev_log_index > 0 and prev_log_index <= len(self.node.persistent.log):
                prev_log_term = self.node.persistent.log[prev_log_index - 1].term
            
            response = self.client.send_heartbeat(
                endpoint, term, leader_id, prev_log_index, prev_log_term, leader_commit
            )
            
            if response:
                if response.term > term:
                    # 发现更高的任期，转为Follower
                    self.node.become_follower(response.term)
                elif response.success:
                    # 心跳成功
                    self.node.leader_state.match_index[node_id] = prev_log_index
                else:
                    # 心跳失败，可能需要回退日志索引
                    if next_index > 1:
                        self.node.leader_state.next_index[node_id] = next_index - 1
        
        except Exception as e:
            self.logger.error(f"Error sending heartbeat to {node_id}: {e}")
    
    def _replicate_logs(self) -> None:
        """复制日志到Follower"""
        # 这里可以实现日志复制逻辑
        # 暂时简化实现
        pass
    
    def _update_commit_index(self) -> None:
        """更新commit_index"""
        if not self.node.is_leader():
            return
        
        # 找到大多数节点都已复制的最高日志索引
        match_indices = list(self.node.leader_state.match_index.values())
        match_indices.append(len(self.node.persistent.log))  # 包括自己
        match_indices.sort(reverse=True)
        
        # 需要大多数节点同意
        majority = len(self.cluster_endpoints) // 2 + 1
        if len(match_indices) >= majority:
            new_commit_index = match_indices[majority - 1]
            
            # 只能提交当前任期的日志
            if (new_commit_index > self.node.volatile.commit_index and
                new_commit_index <= len(self.node.persistent.log)):
                
                log_entry = self.node.persistent.log[new_commit_index - 1]
                if log_entry.term == self.node.get_current_term():
                    self.node.volatile.commit_index = new_commit_index
                    self.logger.debug(f"Updated commit_index to {new_commit_index}")
    
    def _start_election(self) -> bool:
        """开始选举"""
        if not self.cluster_endpoints:
            # 单节点集群，直接成为Leader
            return True
        
        term = self.node.get_current_term()
        candidate_id = self.config.node_id
        last_log_index, last_log_term = self.node.get_last_log_info()
        
        self.logger.info(f"Starting election for term {term}")
        
        # 请求投票
        endpoints = list(self.cluster_endpoints.values())
        votes = self.client.request_votes(endpoints, term, candidate_id, last_log_index, last_log_term)
        
        # 统计投票结果
        vote_count = 1  # 自己的票
        for endpoint, granted in votes.items():
            if granted:
                vote_count += 1
        
        # 检查是否获得大多数投票
        total_nodes = len(self.cluster_endpoints) + 1  # 包括自己
        majority = total_nodes // 2 + 1
        
        success = vote_count >= majority
        self.logger.info(f"Election result: {vote_count}/{total_nodes} votes, majority={majority}, success={success}")
        
        return success
    
    def _apply_logs_loop(self) -> None:
        """应用日志循环"""
        while self._running:
            try:
                # 应用已提交但未应用的日志
                while (self.node.volatile.last_applied < self.node.volatile.commit_index and
                       self.node.volatile.last_applied < len(self.node.persistent.log)):
                    
                    log_index = self.node.volatile.last_applied + 1
                    log_entry = self.node.persistent.log[log_index - 1]
                    
                    # 应用日志条目
                    if self.log_apply_callback:
                        try:
                            success = self.log_apply_callback(log_entry)
                            if success:
                                self.node.volatile.last_applied = log_index
                                self.logger.debug(f"Applied log entry {log_index}")
                            else:
                                self.logger.error(f"Failed to apply log entry {log_index}")
                                break
                        except Exception as e:
                            self.logger.error(f"Error applying log entry {log_index}: {e}")
                            break
                    else:
                        # 没有回调函数，直接标记为已应用
                        self.node.volatile.last_applied = log_index
                
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"Error in apply logs loop: {e}")
                time.sleep(1.0)
    
    def append_log(self, log_type: str, obj: str, data: str) -> bool:
        """添加日志条目（仅Leader可以调用）"""
        if not self.node.is_leader():
            self.logger.warning("Only leader can append logs")
            return False
        
        # 创建日志条目
        entry = LogEntry(
            term=self.node.get_current_term(),
            index=len(self.node.persistent.log) + 1,
            type=log_type,
            object=obj,
            data=data,
            timestamp=int(time.time() * 1000)
        )
        
        # 添加到本地日志
        self.node.append_log_entry(entry)
        
        self.logger.info(f"Appended log entry: {log_type} {obj}")
        return True
    
    def get_status(self) -> dict:
        """获取引擎状态"""
        status = self.node.get_node_status_info()
        status['running'] = self._running
        status['cluster_size'] = len(self.cluster_endpoints) + 1
        return status
