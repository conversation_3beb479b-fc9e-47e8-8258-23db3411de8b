syntax = "proto3";

package raft;

// Raft服务定义
service RaftService {
    // 心跳/日志复制 - Leader向Follower发送
    rpc AppendEntries(AppendEntriesRequest) returns (AppendEntriesResponse);
    
    // 选举投票 - Candidate向其他节点请求投票
    rpc RequestVote(RequestVoteRequest) returns (RequestVoteResponse);
    
    // 获取节点状态
    rpc GetNodeStatus(GetNodeStatusRequest) returns (GetNodeStatusResponse);
    
    // 安装快照 (可选，用于日志压缩)
    rpc InstallSnapshot(InstallSnapshotRequest) returns (InstallSnapshotResponse);
}

// 日志条目
message LogEntry {
    int64 term = 1;          // 任期号
    int64 index = 2;         // 日志索引
    string type = 3;         // 操作类型: create, update, delete
    string object = 4;       // 操作对象: network, eip_binding, eip_snat, eip_dnat
    string data = 5;         // 操作数据 (JSON格式)
    int64 timestamp = 6;     // 时间戳
}

// 心跳/日志复制请求
message AppendEntriesRequest {
    int64 term = 1;              // Leader的当前任期
    string leader_id = 2;        // Leader的节点ID
    int64 prev_log_index = 3;    // 前一个日志条目的索引
    int64 prev_log_term = 4;     // 前一个日志条目的任期
    repeated LogEntry entries = 5; // 要复制的日志条目(心跳时为空)
    int64 leader_commit = 6;     // Leader的已提交索引
}

// 心跳/日志复制响应
message AppendEntriesResponse {
    int64 term = 1;          // 当前任期，用于Leader更新自己
    bool success = 2;        // 如果Follower包含匹配prevLogIndex和prevLogTerm的条目则为true
    int64 last_log_index = 3; // Follower的最后日志索引，用于快速回退
    string node_id = 4;      // 响应节点的ID
}

// 选举投票请求
message RequestVoteRequest {
    int64 term = 1;          // Candidate的任期
    string candidate_id = 2;  // 请求投票的Candidate ID
    int64 last_log_index = 3; // Candidate最后日志条目的索引
    int64 last_log_term = 4;  // Candidate最后日志条目的任期
}

// 选举投票响应
message RequestVoteResponse {
    int64 term = 1;          // 当前任期，用于Candidate更新自己
    bool vote_granted = 2;   // 如果Candidate获得投票则为true
    string node_id = 3;      // 投票节点的ID
}

// 获取节点状态请求
message GetNodeStatusRequest {
    string node_id = 1;      // 请求节点的ID
}

// 节点状态
enum NodeRole {
    FOLLOWER = 0;
    CANDIDATE = 1;
    LEADER = 2;
}

enum NodeStatus {
    RUNNING = 0;
    ERROR = 1;
    STOPPED = 2;
}

// 获取节点状态响应
message GetNodeStatusResponse {
    string node_id = 1;          // 节点ID
    string group_id = 2;         // 节点分组标识
    string endpoint = 3;         // 节点地址
    NodeRole role = 4;           // 节点角色
    NodeStatus status = 5;       // 节点状态
    int64 term = 6;              // 当前任期
    int64 commit_index = 7;      // 已提交日志索引
    int64 last_applied = 8;      // 最后应用日志索引
    int64 last_heartbeat = 9;    // 最后一次心跳时间
    string leader_id = 10;       // 当前Leader的ID
}

// 安装快照请求 (可选功能)
message InstallSnapshotRequest {
    int64 term = 1;              // Leader的当前任期
    string leader_id = 2;        // Leader的节点ID
    int64 last_included_index = 3; // 快照中包含的最后日志条目的索引
    int64 last_included_term = 4;  // 快照中包含的最后日志条目的任期
    int64 offset = 5;            // 分块传输的偏移量
    bytes data = 6;              // 快照数据
    bool done = 7;               // 如果这是最后一个分块则为true
}

// 安装快照响应
message InstallSnapshotResponse {
    int64 term = 1;              // 当前任期
    string node_id = 2;          // 响应节点的ID
    bool success = 3;            // 安装是否成功
}
