# V-Switch gRPC Raft 协议实现

本目录包含了基于gRPC的Raft协议实现，用于v-switch项目的分布式一致性管理。

## 目录结构

```
grpc/
├── __init__.py              # 模块初始化
├── raft.proto              # Protocol Buffers定义
├── raft_pb2.py             # 生成的protobuf消息类
├── raft_pb2_grpc.py        # 生成的gRPC服务类
├── raft_node.py            # Raft节点状态管理
├── raft_server.py          # gRPC服务端实现
├── raft_client.py          # gRPC客户端实现
├── raft_engine.py          # Raft协议核心引擎
├── database.py             # 数据库操作模块
├── demo_server.py          # 演示服务端程序
├── demo_client.py          # 演示客户端程序
├── test_raft.py            # 测试脚本
└── README.md               # 本文档
```

## 功能特性

### Raft协议核心功能
- **Leader选举**: 实现分布式Leader选举机制
- **日志复制**: Leader向Follower复制日志条目
- **心跳机制**: 维持集群节点间的通信
- **状态管理**: 管理节点的Follower/Candidate/Leader状态

### gRPC服务接口
- `AppendEntries`: 心跳和日志复制
- `RequestVote`: 选举投票
- `GetNodeStatus`: 获取节点状态
- `InstallSnapshot`: 快照安装（预留）

### 数据库集成
- 支持MariaDB数据库存储
- `raft_status`表：记录节点状态信息
- `operation_log`表：记录操作日志

## 快速开始

### 1. 安装依赖

```bash
# 安装gRPC工具
pip install grpcio grpcio-tools

# 安装数据库驱动（可选）
pip install pymysql
```

### 2. 生成gRPC代码

```bash
cd src/v_switch/grpc
python -m grpc_tools.protoc --proto_path=. --python_out=. --grpc_python_out=. raft.proto
```

### 3. 运行测试

```bash
# 运行演示测试
python test_raft.py --demo

# 运行单元测试
python test_raft.py --unit

# 运行所有测试
python test_raft.py --all
```

### 4. 启动演示集群

#### 启动三个节点的集群：

```bash
# 终端1 - 启动节点1
python demo_server.py --node-id node1 --port 50051 --cluster node1:50051,node2:50052,node3:50053

# 终端2 - 启动节点2  
python demo_server.py --node-id node2 --port 50052 --cluster node1:50051,node2:50052,node3:50053

# 终端3 - 启动节点3
python demo_server.py --node-id node3 --port 50053 --cluster node1:50051,node2:50052,node3:50053
```

#### 使用客户端监控集群：

```bash
# 查看集群状态
python demo_client.py --endpoints 50051,50052,50053 status

# 监控集群状态
python demo_client.py --endpoints 50051,50052,50053 monitor --duration 60

# 发送测试日志
python demo_client.py --endpoints 50051,50052,50053 test-log --endpoint localhost:50051 --type create --object network
```

## 配置说明

### 节点配置

```python
config = RaftNodeConfig(
    node_id="node1",                    # 节点唯一标识
    group_id="cluster1",                # 集群组标识
    endpoint="localhost:50051",         # 节点监听地址
    election_timeout_min=5.0,           # 选举超时最小值(秒)
    election_timeout_max=10.0,          # 选举超时最大值(秒)
    heartbeat_interval=2.0,             # 心跳间隔(秒)
    request_timeout=3.0                 # 请求超时(秒)
)
```

### 数据库配置

```python
db_config = DatabaseConfig(
    host="localhost",                   # 数据库主机
    port=3306,                         # 数据库端口
    user="root",                       # 数据库用户
    password="password",               # 数据库密码
    database="cloudlink",              # 数据库名称
    charset="utf8mb4"                  # 字符集
)
```

## API 使用示例

### 创建Raft引擎

```python
from v_switch.grpc.raft_engine import RaftEngine
from v_switch.grpc.raft_node import RaftNodeConfig

# 配置节点
config = RaftNodeConfig(
    node_id="node1",
    group_id="cluster1", 
    endpoint="localhost:50051"
)

# 集群端点配置
cluster_endpoints = {
    "node2": "localhost:50052",
    "node3": "localhost:50053"
}

# 创建引擎
engine = RaftEngine(config, cluster_endpoints)

# 启动引擎
engine.start()

# 添加日志条目（仅Leader可以）
if engine.node.is_leader():
    engine.append_log("create", "network", '{"subnet": "***********/24"}')

# 获取状态
status = engine.get_status()
print(f"Node role: {status['role']}")
print(f"Current term: {status['term']}")

# 停止引擎
engine.stop()
```

### 使用gRPC客户端

```python
from v_switch.grpc.raft_client import RaftClient

# 创建客户端
client = RaftClient("client1", timeout=5.0)

# 获取节点状态
status = client.get_node_status("localhost:50051")
print(f"Node status: {status}")

# 发送心跳
response = client.send_heartbeat(
    endpoint="localhost:50051",
    term=1,
    leader_id="node1", 
    prev_log_index=0,
    prev_log_term=0,
    leader_commit=0
)

# 关闭连接
client.close_connections()
```

## 数据库表结构

### raft_status表

```sql
CREATE TABLE raft_status (
    id INT AUTO_INCREMENT PRIMARY KEY,
    node_id VARCHAR(64) NOT NULL UNIQUE,
    group_id VARCHAR(64) NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    role ENUM('leader', 'follower', 'candidate') NOT NULL DEFAULT 'follower',
    status ENUM('running', 'error') NOT NULL DEFAULT 'running',
    term BIGINT NOT NULL DEFAULT 0,
    commit_index BIGINT NOT NULL DEFAULT 0,
    last_applied BIGINT NOT NULL DEFAULT 0,
    last_heartbeat BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### operation_log表

```sql
CREATE TABLE operation_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    node_id VARCHAR(64) NOT NULL,
    log_index BIGINT NOT NULL,
    term BIGINT NOT NULL,
    type ENUM('create', 'update', 'delete') NOT NULL,
    object ENUM('network', 'eip_binding', 'eip_snat', 'eip_dnat') NOT NULL,
    data JSON NOT NULL,
    timestamp BIGINT NOT NULL,
    status ENUM('success', 'failed') NOT NULL DEFAULT 'success',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_node_index (node_id, log_index)
);
```

## 故障排除

### 常见问题

1. **gRPC连接失败**
   - 检查端口是否被占用
   - 确认防火墙设置
   - 验证网络连通性

2. **选举超时**
   - 调整`election_timeout_min/max`参数
   - 检查网络延迟
   - 确认集群配置正确

3. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 确认数据库权限

### 日志调试

```python
import logging

# 启用详细日志
logging.basicConfig(level=logging.DEBUG)

# 或者只启用Raft相关日志
logging.getLogger('raft').setLevel(logging.DEBUG)
```

## 性能调优

### 参数调优建议

- **选举超时**: 根据网络延迟调整，通常5-10秒
- **心跳间隔**: 设置为选举超时的1/5到1/10
- **请求超时**: 设置为心跳间隔的1.5倍
- **线程池大小**: 根据并发需求调整

### 监控指标

- 选举次数和频率
- 心跳成功率
- 日志复制延迟
- 节点状态变化

## 扩展开发

### 添加新的日志类型

1. 在`raft.proto`中添加新的枚举值
2. 重新生成gRPC代码
3. 在应用回调中处理新类型

### 自定义状态机

```python
def custom_log_apply_callback(log_entry):
    """自定义日志应用回调"""
    if log_entry.type == "create" and log_entry.object == "network":
        # 处理网络创建逻辑
        data = json.loads(log_entry.data)
        # ... 业务逻辑
        return True
    return False

engine = RaftEngine(config, cluster_endpoints, custom_log_apply_callback)
```

## 许可证

本项目遵循MIT许可证。
