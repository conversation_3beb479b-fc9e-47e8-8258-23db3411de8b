"""
Raft gRPC客户端实现
"""

import logging
import grpc
from typing import Dict, Optional, List
from concurrent.futures import ThreadPoolExecutor, as_completed

from .raft_pb2 import (
    AppendEntriesRequest, AppendEntriesResponse,
    RequestVoteRequest, RequestVoteResponse,
    GetNodeStatusRequest, GetNodeStatusResponse,
    LogEntry
)
from .raft_pb2_grpc import RaftServiceStub


class RaftClient:
    """Raft客户端，用于节点间通信"""
    
    def __init__(self, node_id: str, request_timeout: float = 3.0):
        """初始化客户端
        
        Args:
            node_id: 当前节点ID
            request_timeout: 请求超时时间
        """
        self.node_id = node_id
        self.request_timeout = request_timeout
        self.logger = logging.getLogger(f"raft.client.{node_id}")
        
        # 连接池
        self._channels: Dict[str, grpc.Channel] = {}
        self._stubs: Dict[str, RaftServiceStub] = {}
    
    def _get_stub(self, endpoint: str) -> Optional[RaftServiceStub]:
        """获取gRPC stub"""
        try:
            if endpoint not in self._stubs:
                # 创建新的连接
                channel = grpc.insecure_channel(endpoint)
                self._channels[endpoint] = channel
                self._stubs[endpoint] = RaftServiceStub(channel)
                self.logger.debug(f"Created connection to {endpoint}")
            
            return self._stubs[endpoint]
        except Exception as e:
            self.logger.error(f"Failed to create stub for {endpoint}: {e}")
            return None
    
    def close_connections(self):
        """关闭所有连接"""
        for endpoint, channel in self._channels.items():
            try:
                channel.close()
                self.logger.debug(f"Closed connection to {endpoint}")
            except Exception as e:
                self.logger.error(f"Error closing connection to {endpoint}: {e}")
        
        self._channels.clear()
        self._stubs.clear()
    
    def send_append_entries(self, endpoint: str, request: AppendEntriesRequest) -> Optional[AppendEntriesResponse]:
        """发送心跳/日志复制请求"""
        stub = self._get_stub(endpoint)
        if not stub:
            return None
        
        try:
            response = stub.AppendEntries(request, timeout=self.request_timeout)
            self.logger.debug(f"AppendEntries to {endpoint}: success={response.success}")
            return response
        except grpc.RpcError as e:
            self.logger.warning(f"AppendEntries to {endpoint} failed: {e.code()}")
            return None
        except Exception as e:
            self.logger.error(f"AppendEntries to {endpoint} error: {e}")
            return None
    
    def send_request_vote(self, endpoint: str, request: RequestVoteRequest) -> Optional[RequestVoteResponse]:
        """发送选举投票请求"""
        stub = self._get_stub(endpoint)
        if not stub:
            return None
        
        try:
            response = stub.RequestVote(request, timeout=self.request_timeout)
            self.logger.debug(f"RequestVote to {endpoint}: granted={response.vote_granted}")
            return response
        except grpc.RpcError as e:
            self.logger.warning(f"RequestVote to {endpoint} failed: {e.code()}")
            return None
        except Exception as e:
            self.logger.error(f"RequestVote to {endpoint} error: {e}")
            return None
    
    def get_node_status(self, endpoint: str) -> Optional[GetNodeStatusResponse]:
        """获取节点状态"""
        stub = self._get_stub(endpoint)
        if not stub:
            return None
        
        try:
            request = GetNodeStatusRequest(node_id=self.node_id)
            response = stub.GetNodeStatus(request, timeout=self.request_timeout)
            return response
        except grpc.RpcError as e:
            self.logger.warning(f"GetNodeStatus to {endpoint} failed: {e.code()}")
            return None
        except Exception as e:
            self.logger.error(f"GetNodeStatus to {endpoint} error: {e}")
            return None
    
    def broadcast_append_entries(self, endpoints: List[str], request: AppendEntriesRequest) -> Dict[str, Optional[AppendEntriesResponse]]:
        """并行发送心跳/日志复制请求到多个节点"""
        results = {}
        
        if not endpoints:
            return results
        
        with ThreadPoolExecutor(max_workers=min(len(endpoints), 10)) as executor:
            # 提交所有任务
            future_to_endpoint = {
                executor.submit(self.send_append_entries, endpoint, request): endpoint
                for endpoint in endpoints
            }
            
            # 收集结果
            for future in as_completed(future_to_endpoint):
                endpoint = future_to_endpoint[future]
                try:
                    response = future.result()
                    results[endpoint] = response
                except Exception as e:
                    self.logger.error(f"Error in broadcast_append_entries to {endpoint}: {e}")
                    results[endpoint] = None
        
        return results
    
    def broadcast_request_vote(self, endpoints: List[str], request: RequestVoteRequest) -> Dict[str, Optional[RequestVoteResponse]]:
        """并行发送选举投票请求到多个节点"""
        results = {}
        
        if not endpoints:
            return results
        
        with ThreadPoolExecutor(max_workers=min(len(endpoints), 10)) as executor:
            # 提交所有任务
            future_to_endpoint = {
                executor.submit(self.send_request_vote, endpoint, request): endpoint
                for endpoint in endpoints
            }
            
            # 收集结果
            for future in as_completed(future_to_endpoint):
                endpoint = future_to_endpoint[future]
                try:
                    response = future.result()
                    results[endpoint] = response
                except Exception as e:
                    self.logger.error(f"Error in broadcast_request_vote to {endpoint}: {e}")
                    results[endpoint] = None
        
        return results
    
    def send_heartbeat(self, endpoint: str, term: int, leader_id: str, 
                      prev_log_index: int, prev_log_term: int, 
                      leader_commit: int) -> Optional[AppendEntriesResponse]:
        """发送心跳（空的AppendEntries）"""
        request = AppendEntriesRequest(
            term=term,
            leader_id=leader_id,
            prev_log_index=prev_log_index,
            prev_log_term=prev_log_term,
            entries=[],  # 心跳时为空
            leader_commit=leader_commit
        )
        
        return self.send_append_entries(endpoint, request)
    
    def send_log_entries(self, endpoint: str, term: int, leader_id: str,
                        prev_log_index: int, prev_log_term: int,
                        entries: List[LogEntry], leader_commit: int) -> Optional[AppendEntriesResponse]:
        """发送日志条目"""
        request = AppendEntriesRequest(
            term=term,
            leader_id=leader_id,
            prev_log_index=prev_log_index,
            prev_log_term=prev_log_term,
            entries=entries,
            leader_commit=leader_commit
        )
        
        return self.send_append_entries(endpoint, request)
    
    def request_votes(self, endpoints: List[str], term: int, candidate_id: str,
                     last_log_index: int, last_log_term: int) -> Dict[str, bool]:
        """请求投票"""
        request = RequestVoteRequest(
            term=term,
            candidate_id=candidate_id,
            last_log_index=last_log_index,
            last_log_term=last_log_term
        )
        
        responses = self.broadcast_request_vote(endpoints, request)
        
        # 统计投票结果
        votes = {}
        for endpoint, response in responses.items():
            if response:
                votes[endpoint] = response.vote_granted
            else:
                votes[endpoint] = False
        
        return votes
