#!/usr/bin/env python3
"""
Raft协议测试脚本
"""

from v_switch.grpc.raft_pb2 import LogEntry
from v_switch.grpc.raft_engine import RaftEngine
from v_switch.grpc.raft_client import RaftClient
from v_switch.grpc.raft_node import <PERSON>ft<PERSON><PERSON>, RaftNodeConfig, RaftState
import os
import sys
import time
import unittest
import threading
import subprocess
from typing import List, Dict

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))


class TestRaftNode(unittest.TestCase):
    """测试Raft节点"""

    def setUp(self):
        """设置测试环境"""
        self.config = RaftNodeConfig(
            node_id="test-node-1",
            group_id="test-group",
            endpoint="localhost:50051",
            election_timeout_min=1.0,
            election_timeout_max=2.0,
            heartbeat_interval=0.5
        )
        self.node = RaftNode(self.config)

    def test_initial_state(self):
        """测试初始状态"""
        self.assertEqual(self.node.get_state(), RaftState.FOLLOWER)
        self.assertEqual(self.node.get_current_term(), 0)
        self.assertIsNone(self.node.get_leader_id())
        self.assertFalse(self.node.is_leader())
        self.assertTrue(self.node.is_follower())
        self.assertFalse(self.node.is_candidate())

    def test_state_transitions(self):
        """测试状态转换"""
        # Follower -> Candidate
        self.node.become_candidate()
        self.assertTrue(self.node.is_candidate())
        self.assertEqual(self.node.get_current_term(), 1)
        self.assertEqual(self.node.persistent.voted_for, "test-node-1")

        # Candidate -> Leader
        self.node.become_leader()
        self.assertTrue(self.node.is_leader())
        self.assertEqual(self.node.get_leader_id(), "test-node-1")

        # Leader -> Follower
        self.node.become_follower(2, "other-leader")
        self.assertTrue(self.node.is_follower())
        self.assertEqual(self.node.get_current_term(), 2)
        self.assertEqual(self.node.get_leader_id(), "other-leader")
        self.assertIsNone(self.node.persistent.voted_for)

    def test_log_operations(self):
        """测试日志操作"""
        # 添加日志条目
        entry1 = LogEntry(term=1, index=1, type="create", object="network", data='{"test": 1}', timestamp=1000)
        entry2 = LogEntry(term=1, index=2, type="update", object="network", data='{"test": 2}', timestamp=2000)

        self.node.append_log_entry(entry1)
        self.node.append_log_entry(entry2)

        # 检查日志
        self.assertEqual(len(self.node.persistent.log), 2)

        # 获取最后日志信息
        last_index, last_term = self.node.get_last_log_info()
        self.assertEqual(last_index, 2)
        self.assertEqual(last_term, 1)

        # 获取日志条目
        entries = self.node.get_log_entries(1)
        self.assertEqual(len(entries), 2)
        self.assertEqual(entries[0].index, 1)
        self.assertEqual(entries[1].index, 2)

    def test_cluster_nodes_update(self):
        """测试集群节点更新"""
        nodes = {"node2", "node3", "node4"}
        self.node.update_cluster_nodes(nodes)

        self.assertEqual(self.node.cluster_nodes, nodes)

        # 测试Leader状态下的索引初始化
        self.node.become_leader()
        self.node.update_cluster_nodes(nodes)

        for node_id in nodes:
            self.assertIn(node_id, self.node.leader_state.next_index)
            self.assertIn(node_id, self.node.leader_state.match_index)
            self.assertEqual(self.node.leader_state.match_index[node_id], 0)


class TestRaftClient(unittest.TestCase):
    """测试Raft客户端"""

    def setUp(self):
        """设置测试环境"""
        self.client = RaftClient("test-client", 1.0)

    def tearDown(self):
        """清理测试环境"""
        self.client.close_connections()

    def test_client_initialization(self):
        """测试客户端初始化"""
        self.assertEqual(self.client.node_id, "test-client")
        self.assertEqual(self.client.request_timeout, 1.0)
        self.assertEqual(len(self.client._channels), 0)
        self.assertEqual(len(self.client._stubs), 0)


class TestRaftIntegration(unittest.TestCase):
    """Raft集成测试"""

    def setUp(self):
        """设置测试环境"""
        self.engines = []
        self.ports = [50051, 50052, 50053]
        self.cluster_endpoints = {
            f"node{i+1}": f"localhost:{port}"
            for i, port in enumerate(self.ports)
        }

    def tearDown(self):
        """清理测试环境"""
        for engine in self.engines:
            engine.stop()
        self.engines.clear()

    def create_raft_engine(self, node_id: str, port: int) -> RaftEngine:
        """创建Raft引擎"""
        config = RaftNodeConfig(
            node_id=node_id,
            group_id="test-cluster",
            endpoint=f"localhost:{port}",
            election_timeout_min=1.0,
            election_timeout_max=2.0,
            heartbeat_interval=0.3,
            request_timeout=1.0
        )

        cluster_endpoints = self.cluster_endpoints.copy()
        if node_id in cluster_endpoints:
            del cluster_endpoints[node_id]

        engine = RaftEngine(config, cluster_endpoints)
        self.engines.append(engine)
        return engine

    def test_single_node_cluster(self):
        """测试单节点集群"""
        # 创建单节点集群（没有其他节点）
        config = RaftNodeConfig(
            node_id="node1",
            group_id="test-cluster",
            endpoint="localhost:50051",
            election_timeout_min=1.0,
            election_timeout_max=2.0,
            heartbeat_interval=0.3,
            request_timeout=1.0
        )

        # 空的集群端点（单节点）
        engine = RaftEngine(config, {})
        self.engines.append(engine)
        engine.start()

        # 等待一段时间让节点稳定，单节点集群应该很快成为Leader
        max_wait = 3.0
        wait_time = 0.1
        elapsed = 0.0

        while elapsed < max_wait and not engine.node.is_leader():
            time.sleep(wait_time)
            elapsed += wait_time

        # 单节点应该成为Leader
        self.assertTrue(engine.node.is_leader(), f"Node should be leader after {elapsed}s")

        # 测试添加日志
        success = engine.append_log("create", "network", '{"test": "data"}')
        self.assertTrue(success)

        engine.stop()

    def test_three_node_cluster_simulation(self):
        """测试三节点集群模拟（不启动实际gRPC服务）"""
        # 创建三个引擎但不启动gRPC服务
        engines = []
        for i, port in enumerate(self.ports):
            engine = self.create_raft_engine(f"node{i+1}", port)
            engines.append(engine)

        # 测试初始状态
        for engine in engines:
            self.assertTrue(engine.node.is_follower())
            self.assertEqual(engine.node.get_current_term(), 0)

        # 模拟选举过程
        # 让第一个节点成为Candidate
        engines[0].node.become_candidate()
        self.assertTrue(engines[0].node.is_candidate())
        self.assertEqual(engines[0].node.get_current_term(), 1)

        # 模拟获得投票后成为Leader
        engines[0].node.become_leader()
        self.assertTrue(engines[0].node.is_leader())

        # 其他节点成为Follower
        for engine in engines[1:]:
            engine.node.become_follower(1, "node1")
            self.assertTrue(engine.node.is_follower())
            self.assertEqual(engine.node.get_leader_id(), "node1")


def run_demo_test():
    """运行演示测试"""
    print("Running Raft Demo Test...")
    print("="*50)

    # 测试基本功能
    print("1. Testing basic Raft node functionality...")

    config = RaftNodeConfig(
        node_id="demo-node",
        group_id="demo-group",
        endpoint="localhost:50051"
    )

    node = RaftNode(config)
    print(f"   Initial state: {node.get_state().value}")
    print(f"   Initial term: {node.get_current_term()}")

    # 测试状态转换
    node.become_candidate()
    print(f"   After become_candidate: {node.get_state().value}, term: {node.get_current_term()}")

    node.become_leader()
    print(f"   After become_leader: {node.get_state().value}")

    # 测试日志操作
    entry = LogEntry(
        term=1, index=1, type="create", object="network",
        data='{"subnet": "***********/24"}', timestamp=int(time.time() * 1000)
    )
    node.append_log_entry(entry)
    print(f"   Log entries count: {len(node.persistent.log)}")

    last_index, last_term = node.get_last_log_info()
    print(f"   Last log info: index={last_index}, term={last_term}")

    print("   Basic functionality test completed ✓")
    print()

    # 测试客户端
    print("2. Testing Raft client...")
    client = RaftClient("demo-client")
    print(f"   Client node_id: {client.node_id}")
    print(f"   Client timeout: {client.request_timeout}")
    client.close_connections()
    print("   Client test completed ✓")
    print()

    print("Demo test completed successfully!")
    print("="*50)


if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='Raft Test Suite')
    parser.add_argument('--demo', action='store_true', help='Run demo test')
    parser.add_argument('--unit', action='store_true', help='Run unit tests')
    parser.add_argument('--all', action='store_true', help='Run all tests')

    args = parser.parse_args()

    if args.demo or args.all:
        run_demo_test()
        print()

    if args.unit or args.all:
        # 运行单元测试
        unittest.main(argv=[''], exit=False, verbosity=2)

    if not any([args.demo, args.unit, args.all]):
        parser.print_help()
