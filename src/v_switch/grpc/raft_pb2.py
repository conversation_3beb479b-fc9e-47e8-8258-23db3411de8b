# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: raft.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'raft.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nraft.proto\x12\x04raft\"f\n\x08LogEntry\x12\x0c\n\x04term\x18\x01 \x01(\x03\x12\r\n\x05index\x18\x02 \x01(\x03\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x0e\n\x06object\x18\x04 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x05 \x01(\t\x12\x11\n\ttimestamp\x18\x06 \x01(\x03\"\x9e\x01\n\x14\x41ppendEntriesRequest\x12\x0c\n\x04term\x18\x01 \x01(\x03\x12\x11\n\tleader_id\x18\x02 \x01(\t\x12\x16\n\x0eprev_log_index\x18\x03 \x01(\x03\x12\x15\n\rprev_log_term\x18\x04 \x01(\x03\x12\x1f\n\x07\x65ntries\x18\x05 \x03(\x0b\x32\x0e.raft.LogEntry\x12\x15\n\rleader_commit\x18\x06 \x01(\x03\"_\n\x15\x41ppendEntriesResponse\x12\x0c\n\x04term\x18\x01 \x01(\x03\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x16\n\x0elast_log_index\x18\x03 \x01(\x03\x12\x0f\n\x07node_id\x18\x04 \x01(\t\"g\n\x12RequestVoteRequest\x12\x0c\n\x04term\x18\x01 \x01(\x03\x12\x14\n\x0c\x63\x61ndidate_id\x18\x02 \x01(\t\x12\x16\n\x0elast_log_index\x18\x03 \x01(\x03\x12\x15\n\rlast_log_term\x18\x04 \x01(\x03\"J\n\x13RequestVoteResponse\x12\x0c\n\x04term\x18\x01 \x01(\x03\x12\x14\n\x0cvote_granted\x18\x02 \x01(\x08\x12\x0f\n\x07node_id\x18\x03 \x01(\t\"\'\n\x14GetNodeStatusRequest\x12\x0f\n\x07node_id\x18\x01 \x01(\t\"\xf1\x01\n\x15GetNodeStatusResponse\x12\x0f\n\x07node_id\x18\x01 \x01(\t\x12\x10\n\x08group_id\x18\x02 \x01(\t\x12\x10\n\x08\x65ndpoint\x18\x03 \x01(\t\x12\x1c\n\x04role\x18\x04 \x01(\x0e\x32\x0e.raft.NodeRole\x12 \n\x06status\x18\x05 \x01(\x0e\x32\x10.raft.NodeStatus\x12\x0c\n\x04term\x18\x06 \x01(\x03\x12\x14\n\x0c\x63ommit_index\x18\x07 \x01(\x03\x12\x14\n\x0clast_applied\x18\x08 \x01(\x03\x12\x16\n\x0elast_heartbeat\x18\t \x01(\x03\x12\x11\n\tleader_id\x18\n \x01(\t\"\x9e\x01\n\x16InstallSnapshotRequest\x12\x0c\n\x04term\x18\x01 \x01(\x03\x12\x11\n\tleader_id\x18\x02 \x01(\t\x12\x1b\n\x13last_included_index\x18\x03 \x01(\x03\x12\x1a\n\x12last_included_term\x18\x04 \x01(\x03\x12\x0e\n\x06offset\x18\x05 \x01(\x03\x12\x0c\n\x04\x64\x61ta\x18\x06 \x01(\x0c\x12\x0c\n\x04\x64one\x18\x07 \x01(\x08\"I\n\x17InstallSnapshotResponse\x12\x0c\n\x04term\x18\x01 \x01(\x03\x12\x0f\n\x07node_id\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08*3\n\x08NodeRole\x12\x0c\n\x08\x46OLLOWER\x10\x00\x12\r\n\tCANDIDATE\x10\x01\x12\n\n\x06LEADER\x10\x02*1\n\nNodeStatus\x12\x0b\n\x07RUNNING\x10\x00\x12\t\n\x05\x45RROR\x10\x01\x12\x0b\n\x07STOPPED\x10\x02\x32\xb5\x02\n\x0bRaftService\x12H\n\rAppendEntries\x12\x1a.raft.AppendEntriesRequest\x1a\x1b.raft.AppendEntriesResponse\x12\x42\n\x0bRequestVote\x12\x18.raft.RequestVoteRequest\x1a\x19.raft.RequestVoteResponse\x12H\n\rGetNodeStatus\x12\x1a.raft.GetNodeStatusRequest\x1a\x1b.raft.GetNodeStatusResponse\x12N\n\x0fInstallSnapshot\x12\x1c.raft.InstallSnapshotRequest\x1a\x1d.raft.InstallSnapshotResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'raft_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_NODEROLE']._serialized_start=1084
  _globals['_NODEROLE']._serialized_end=1135
  _globals['_NODESTATUS']._serialized_start=1137
  _globals['_NODESTATUS']._serialized_end=1186
  _globals['_LOGENTRY']._serialized_start=20
  _globals['_LOGENTRY']._serialized_end=122
  _globals['_APPENDENTRIESREQUEST']._serialized_start=125
  _globals['_APPENDENTRIESREQUEST']._serialized_end=283
  _globals['_APPENDENTRIESRESPONSE']._serialized_start=285
  _globals['_APPENDENTRIESRESPONSE']._serialized_end=380
  _globals['_REQUESTVOTEREQUEST']._serialized_start=382
  _globals['_REQUESTVOTEREQUEST']._serialized_end=485
  _globals['_REQUESTVOTERESPONSE']._serialized_start=487
  _globals['_REQUESTVOTERESPONSE']._serialized_end=561
  _globals['_GETNODESTATUSREQUEST']._serialized_start=563
  _globals['_GETNODESTATUSREQUEST']._serialized_end=602
  _globals['_GETNODESTATUSRESPONSE']._serialized_start=605
  _globals['_GETNODESTATUSRESPONSE']._serialized_end=846
  _globals['_INSTALLSNAPSHOTREQUEST']._serialized_start=849
  _globals['_INSTALLSNAPSHOTREQUEST']._serialized_end=1007
  _globals['_INSTALLSNAPSHOTRESPONSE']._serialized_start=1009
  _globals['_INSTALLSNAPSHOTRESPONSE']._serialized_end=1082
  _globals['_RAFTSERVICE']._serialized_start=1189
  _globals['_RAFTSERVICE']._serialized_end=1498
# @@protoc_insertion_point(module_scope)
