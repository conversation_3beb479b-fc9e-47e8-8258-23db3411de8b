"""
Raft gRPC服务端实现
"""

import logging
import grpc
from typing import Optional

from .raft_pb2 import (
    AppendEntriesRequest, AppendEntriesResponse,
    RequestVoteRequest, RequestVoteResponse,
    GetNodeStatusRequest, GetNodeStatusResponse,
    InstallSnapshotRequest, InstallSnapshotResponse,
    NodeRole, NodeStatus
)
from .raft_pb2_grpc import RaftServiceServicer
from .raft_node import RaftNode, RaftState


class RaftServiceImpl(RaftServiceServicer):
    """Raft服务实现"""
    
    def __init__(self, raft_node: RaftNode):
        """初始化服务
        
        Args:
            raft_node: Raft节点实例
        """
        self.raft_node = raft_node
        self.logger = logging.getLogger(f"raft.server.{raft_node.config.node_id}")
    
    def AppendEntries(self, request: AppendEntriesRequest, context) -> AppendEntriesResponse:
        """处理心跳/日志复制请求"""
        try:
            self.logger.debug(f"Received AppendEntries from {request.leader_id}, term={request.term}")
            
            current_term = self.raft_node.get_current_term()
            
            # 如果请求的任期小于当前任期，拒绝
            if request.term < current_term:
                self.logger.debug(f"Rejecting AppendEntries: term {request.term} < {current_term}")
                return AppendEntriesResponse(
                    term=current_term,
                    success=False,
                    last_log_index=len(self.raft_node.persistent.log),
                    node_id=self.raft_node.config.node_id
                )
            
            # 如果请求的任期大于当前任期，转为Follower
            if request.term > current_term:
                self.raft_node.become_follower(request.term, request.leader_id)
            elif self.raft_node.is_candidate():
                # 如果是Candidate状态，收到同任期的AppendEntries，转为Follower
                self.raft_node.become_follower(request.term, request.leader_id)
            
            # 更新心跳时间
            self.raft_node.update_heartbeat()
            
            # 检查日志一致性
            if request.prev_log_index > 0:
                # 检查是否有对应的日志条目
                if request.prev_log_index > len(self.raft_node.persistent.log):
                    self.logger.debug(f"Log inconsistency: prev_log_index {request.prev_log_index} > log length {len(self.raft_node.persistent.log)}")
                    return AppendEntriesResponse(
                        term=self.raft_node.get_current_term(),
                        success=False,
                        last_log_index=len(self.raft_node.persistent.log),
                        node_id=self.raft_node.config.node_id
                    )
                
                # 检查任期是否匹配
                prev_log = self.raft_node.persistent.log[request.prev_log_index - 1]
                if prev_log.term != request.prev_log_term:
                    self.logger.debug(f"Log inconsistency: prev_log_term {prev_log.term} != {request.prev_log_term}")
                    return AppendEntriesResponse(
                        term=self.raft_node.get_current_term(),
                        success=False,
                        last_log_index=len(self.raft_node.persistent.log),
                        node_id=self.raft_node.config.node_id
                    )
            
            # 处理新的日志条目
            if request.entries:
                # 删除冲突的日志条目
                start_index = request.prev_log_index
                if start_index < len(self.raft_node.persistent.log):
                    self.raft_node.persistent.log = self.raft_node.persistent.log[:start_index]
                
                # 添加新的日志条目
                for entry in request.entries:
                    self.raft_node.append_log_entry(entry)
                
                self.logger.debug(f"Appended {len(request.entries)} log entries")
            
            # 更新commit_index
            if request.leader_commit > self.raft_node.volatile.commit_index:
                self.raft_node.volatile.commit_index = min(
                    request.leader_commit,
                    len(self.raft_node.persistent.log)
                )
                self.logger.debug(f"Updated commit_index to {self.raft_node.volatile.commit_index}")
            
            return AppendEntriesResponse(
                term=self.raft_node.get_current_term(),
                success=True,
                last_log_index=len(self.raft_node.persistent.log),
                node_id=self.raft_node.config.node_id
            )
            
        except Exception as e:
            self.logger.error(f"Error in AppendEntries: {e}")
            return AppendEntriesResponse(
                term=self.raft_node.get_current_term(),
                success=False,
                last_log_index=len(self.raft_node.persistent.log),
                node_id=self.raft_node.config.node_id
            )
    
    def RequestVote(self, request: RequestVoteRequest, context) -> RequestVoteResponse:
        """处理选举投票请求"""
        try:
            self.logger.debug(f"Received RequestVote from {request.candidate_id}, term={request.term}")
            
            current_term = self.raft_node.get_current_term()
            
            # 如果请求的任期小于当前任期，拒绝投票
            if request.term < current_term:
                self.logger.debug(f"Rejecting vote: term {request.term} < {current_term}")
                return RequestVoteResponse(
                    term=current_term,
                    vote_granted=False,
                    node_id=self.raft_node.config.node_id
                )
            
            # 如果请求的任期大于当前任期，更新任期并转为Follower
            if request.term > current_term:
                self.raft_node.become_follower(request.term)
            
            # 检查是否已经投票
            voted_for = self.raft_node.persistent.voted_for
            if voted_for is not None and voted_for != request.candidate_id:
                self.logger.debug(f"Already voted for {voted_for} in term {request.term}")
                return RequestVoteResponse(
                    term=self.raft_node.get_current_term(),
                    vote_granted=False,
                    node_id=self.raft_node.config.node_id
                )
            
            # 检查候选人的日志是否至少和自己一样新
            last_log_index, last_log_term = self.raft_node.get_last_log_info()
            
            log_ok = (request.last_log_term > last_log_term or 
                     (request.last_log_term == last_log_term and 
                      request.last_log_index >= last_log_index))
            
            if not log_ok:
                self.logger.debug(f"Candidate log not up-to-date: candidate({request.last_log_term}, {request.last_log_index}) vs self({last_log_term}, {last_log_index})")
                return RequestVoteResponse(
                    term=self.raft_node.get_current_term(),
                    vote_granted=False,
                    node_id=self.raft_node.config.node_id
                )
            
            # 投票给候选人
            self.raft_node.persistent.voted_for = request.candidate_id
            self.raft_node.election_deadline = self.raft_node._reset_election_timeout()
            
            self.logger.info(f"Voted for {request.candidate_id} in term {request.term}")
            
            return RequestVoteResponse(
                term=self.raft_node.get_current_term(),
                vote_granted=True,
                node_id=self.raft_node.config.node_id
            )
            
        except Exception as e:
            self.logger.error(f"Error in RequestVote: {e}")
            return RequestVoteResponse(
                term=self.raft_node.get_current_term(),
                vote_granted=False,
                node_id=self.raft_node.config.node_id
            )
    
    def GetNodeStatus(self, request: GetNodeStatusRequest, context) -> GetNodeStatusResponse:
        """获取节点状态"""
        try:
            status_info = self.raft_node.get_node_status_info()
            
            # 转换状态枚举
            role_map = {
                'follower': NodeRole.FOLLOWER,
                'candidate': NodeRole.CANDIDATE,
                'leader': NodeRole.LEADER
            }
            
            return GetNodeStatusResponse(
                node_id=status_info['node_id'],
                group_id=status_info['group_id'],
                endpoint=status_info['endpoint'],
                role=role_map.get(status_info['role'], NodeRole.FOLLOWER),
                status=status_info['status'],
                term=status_info['term'],
                commit_index=status_info['commit_index'],
                last_applied=status_info['last_applied'],
                last_heartbeat=status_info['last_heartbeat'],
                leader_id=status_info['leader_id']
            )
            
        except Exception as e:
            self.logger.error(f"Error in GetNodeStatus: {e}")
            return GetNodeStatusResponse(
                node_id=self.raft_node.config.node_id,
                group_id=self.raft_node.config.group_id,
                endpoint=self.raft_node.config.endpoint,
                role=NodeRole.FOLLOWER,
                status=NodeStatus.ERROR,
                term=0,
                commit_index=0,
                last_applied=0,
                last_heartbeat=0,
                leader_id=""
            )
    
    def InstallSnapshot(self, request: InstallSnapshotRequest, context) -> InstallSnapshotResponse:
        """安装快照（暂未实现）"""
        self.logger.warning("InstallSnapshot not implemented yet")
        return InstallSnapshotResponse(
            term=self.raft_node.get_current_term(),
            node_id=self.raft_node.config.node_id,
            success=False
        )
