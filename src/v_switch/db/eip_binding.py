from sqlalchemy import Column, Integer, String, BigInteger
from . import Base, db_session

class EipBinding(Base):
    __tablename__ = 'eip_binding'

    id = Column(Integer, primary_key=True, autoincrement=True)
    eip = Column(String(50), nullable=False)
    eip_vlan_id = Column(Integer, nullable=False)
    internal_ip = Column(String(50), nullable=False)
    sub_vlan_id = Column(Integer, nullable=False)
    rate = Column(BigInteger)
    ceil = Column(BigInteger)

def create_eip_binding(eip_binding: EipBinding):
    with db_session() as session:
        session.add(eip_binding)
        session.flush()
        session.refresh(eip_binding)
        session.expunge(eip_binding)
        return eip_binding

def get_eip_binding(eip_binding_id: int):
    with db_session() as session:
        eip_binding = session.query(EipBinding).filter(EipBinding.id == eip_binding_id).first()
        if eip_binding:
            session.expunge(eip_binding)
        return eip_binding

def get_eip_bindings(skip: int = 0, limit: int = 100):
    with db_session() as session:
        eip_bindings = session.query(EipBinding).offset(skip).limit(limit).all()
        # expunge all objects from session to prevent DetachedInstanceError
        for binding in eip_bindings:
            session.expunge(binding)
        return eip_bindings

def update_eip_binding(eip_binding_id: int, eip_binding_data: dict):
    with db_session() as session:
        session.query(EipBinding).filter(EipBinding.id == eip_binding_id).update(eip_binding_data)
        updated_binding = session.query(EipBinding).filter(EipBinding.id == eip_binding_id).first()
        if updated_binding:
            session.expunge(updated_binding)
        return updated_binding

def delete_eip_binding(eip_binding_id: int):
    with db_session() as session:
        eip_binding = session.query(EipBinding).filter(EipBinding.id == eip_binding_id).first()
        if eip_binding:
            session.delete(eip_binding)
            session.flush()
            session.expunge(eip_binding)
        return eip_binding
