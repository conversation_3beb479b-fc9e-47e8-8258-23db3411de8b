import pymysql

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.ext.declarative import declarative_base
from functools import wraps
from contextlib import contextmanager

DATABASE_URL = "mysql+pymysql://admin:admin@192.168.1.201:3306/cloudlink-sdn"
connect_args = {
    "charset": "utf8",                 # 对应 characterEncoding=utf-8 和 useUnicode=true
    #"use_affected_rows": True,         # 对应 useAffectedRows=true
    #"multi_statements": True,          # 对应 allowMultiQueries=true
    "ssl_disabled": True,              # 对应 useSSL=false (更明确的 PyMySQL 参数)
    "init_command": "SET time_zone='Asia/Shanghai'",  # 对应 serverTimezone
    #"allow_public_key_retrieval": True  # 对应 allowPublicKeyRetrieval=true
}
engine = create_engine(DATABASE_URL, connect_args=connect_args)
session_factory = sessionmaker(autocommit=False, autoflush=False, bind=engine)
DBSession = scoped_session(session_factory)
Base = declarative_base()
Base.query = DBSession.query_property()


def session_handler(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        session = DBSession()
        try:
            result = func(session, *args, **kwargs)
            session.commit()
            return result
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    return wrapper

@contextmanager
def db_session():
    """Provide a transactional scope around a series of operations."""
    session = DBSession()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


def init_db():
    # Import all modules here that might define models so that
    # they will be registered properly on the metadata. Otherwise
    # you will have to import them first before calling init_db()
    from . import ip_pool, ip_pool_allocated, network, eip_binding, eip_snat, eip_dnat, operation_log, raft_status
    Base.metadata.create_all(bind=engine)
