from sqlalchemy import Column, Integer, String
from . import Base, db_session

class Network(Base):
    __tablename__ = 'network'

    id = Column(Integer, primary_key=True, autoincrement=True)
    server_type = Column(String(50), nullable=False)
    tenant_id = Column(String(255), nullable=False)
    vlan_id = Column(Integer, nullable=False)
    ip_start = Column(String(50), nullable=False)
    ip_end = Column(String(50), nullable=False)
    ip_mask = Column(String(50), nullable=False)
    ip_gateway = Column(String(50), nullable=False)
    ip6_start = Column(String(50))
    ip6_end = Column(String(50))
    ip6_mask = Column(String(50))
    ip6_gateway = Column(String(50))
    monitor_ip = Column(String(50))
    monitor_gateway_ip = Column(String(50))

def create_network(network: Network):
    with db_session() as session:
        session.add(network)
        session.flush()
        session.refresh(network)
        session.expunge(network)
        return network

def get_network(network_id: int):
    with db_session() as session:
        network = session.query(Network).filter(Network.id == network_id).first()
        if network:
            session.expunge(network)
        return network

def get_networks(skip: int = 0, limit: int = 100):
    with db_session() as session:
        networks = session.query(Network).offset(skip).limit(limit).all()
        for network in networks:
            session.expunge(network)
        return networks

def update_network(network_id: int, network_data: dict):
    with db_session() as session:
        session.query(Network).filter(Network.id == network_id).update(network_data)
        updated_network = session.query(Network).filter(Network.id == network_id).first()
        if updated_network:
            session.expunge(updated_network)
        return updated_network

def delete_network(network_id: int):
    with db_session() as session:
        network = session.query(Network).filter(Network.id == network_id).first()
        if network:
            session.delete(network)
            session.flush()
            session.expunge(network)
        return network
