"""
Operation handler for v-switch agent.
Handles operations received from core_service and updates last_applied.
"""

import logging
from typing import Dict, Any, Optional
from v_switch.agent.database_heartbeat_manager import DatabaseHeartbeatManager
from v_switch.agent.command_processor import CommandProcessor
from v_switch.db.redo_log import get_redo_logs_after_id


class OperationHandler:
    """Handles operations received from core_service."""

    def __init__(self, heartbeat_manager: DatabaseHeartbeatManager, 
                 command_processor: CommandProcessor):
        """Initialize operation handler.
        
        Args:
            heartbeat_manager: Database heartbeat manager
            command_processor: Command processor for executing operations
        """
        self.heartbeat_manager = heartbeat_manager
        self.command_processor = command_processor
        self.logger = logging.getLogger(__name__)

    def handle_operation(self, operation_data: Dict[str, Any]) -> bool:
        """Handle a single operation from core_service.
        
        Args:
            operation_data: Operation data containing:
                - operation_type: create, update, delete
                - object_type: network, eip_binding, eip_snat, eip_dnat
                - vlan_id: VLAN identifier
                - redo_log_id: Redo log ID
                - data: Operation-specific data
                
        Returns:
            True if successful
        """
        try:
            operation_type = operation_data.get('operation_type')
            object_type = operation_data.get('object_type')
            vlan_id = operation_data.get('vlan_id')
            redo_log_id = operation_data.get('redo_log_id')
            data = operation_data.get('data', {})

            self.logger.info(f"Handling operation: {operation_type} {object_type} for VLAN {vlan_id}")

            # Execute the operation based on type and object
            success = self._execute_operation(operation_type, object_type, vlan_id, data)

            if success:
                # Update last_applied in heartbeat manager
                self.heartbeat_manager.update_last_applied(redo_log_id)
                self.logger.info(f"Successfully executed operation {redo_log_id}")
                return True
            else:
                self.logger.error(f"Failed to execute operation {redo_log_id}")
                return False

        except Exception as e:
            self.logger.error(f"Error handling operation: {e}")
            return False

    def _execute_operation(self, operation_type: str, object_type: str, 
                          vlan_id: int, data: Dict[str, Any]) -> bool:
        """Execute a specific operation.
        
        Args:
            operation_type: Type of operation
            object_type: Type of object
            vlan_id: VLAN identifier
            data: Operation data
            
        Returns:
            True if successful
        """
        try:
            if object_type == 'network':
                return self._handle_network_operation(operation_type, vlan_id, data)
            elif object_type == 'eip_binding':
                return self._handle_eip_binding_operation(operation_type, vlan_id, data)
            elif object_type == 'eip_snat':
                return self._handle_snat_operation(operation_type, vlan_id, data)
            elif object_type == 'eip_dnat':
                return self._handle_dnat_operation(operation_type, vlan_id, data)
            else:
                self.logger.error(f"Unknown object type: {object_type}")
                return False

        except Exception as e:
            self.logger.error(f"Error executing operation: {e}")
            return False

    def _handle_network_operation(self, operation_type: str, vlan_id: int, 
                                 data: Dict[str, Any]) -> bool:
        """Handle network (subnet) operations.
        
        Args:
            operation_type: create, update, delete
            vlan_id: VLAN identifier
            data: Network data
            
        Returns:
            True if successful
        """
        try:
            if operation_type == 'create':
                self.logger.info(f"Creating network for VLAN {vlan_id}")
                # Here you would call the command processor to create network configuration
                # For now, we'll just log the operation
                return True
            elif operation_type == 'update':
                self.logger.info(f"Updating network for VLAN {vlan_id}")
                return True
            elif operation_type == 'delete':
                self.logger.info(f"Deleting network for VLAN {vlan_id}")
                return True
            else:
                self.logger.error(f"Unknown network operation: {operation_type}")
                return False

        except Exception as e:
            self.logger.error(f"Error handling network operation: {e}")
            return False

    def _handle_eip_binding_operation(self, operation_type: str, vlan_id: int, 
                                     data: Dict[str, Any]) -> bool:
        """Handle EIP binding operations.
        
        Args:
            operation_type: create, update, delete
            vlan_id: VLAN identifier
            data: EIP binding data
            
        Returns:
            True if successful
        """
        try:
            eip = data.get('eip', '')
            internal_ip = data.get('internal_ip', '')
            
            if operation_type == 'create':
                self.logger.info(f"Creating EIP binding {eip} -> {internal_ip} for VLAN {vlan_id}")
                return True
            elif operation_type == 'update':
                self.logger.info(f"Updating EIP binding {eip} -> {internal_ip} for VLAN {vlan_id}")
                return True
            elif operation_type == 'delete':
                self.logger.info(f"Deleting EIP binding {eip} -> {internal_ip} for VLAN {vlan_id}")
                return True
            else:
                self.logger.error(f"Unknown EIP binding operation: {operation_type}")
                return False

        except Exception as e:
            self.logger.error(f"Error handling EIP binding operation: {e}")
            return False

    def _handle_snat_operation(self, operation_type: str, vlan_id: int, 
                              data: Dict[str, Any]) -> bool:
        """Handle SNAT operations.
        
        Args:
            operation_type: create, update, delete
            vlan_id: VLAN identifier
            data: SNAT data
            
        Returns:
            True if successful
        """
        try:
            eip = data.get('eip', '')
            gateway_ip = data.get('gateway_ip', '')
            
            if operation_type == 'create':
                self.logger.info(f"Creating SNAT rule {eip} for gateway {gateway_ip} on VLAN {vlan_id}")
                return True
            elif operation_type == 'delete':
                self.logger.info(f"Deleting SNAT rule {eip} for gateway {gateway_ip} on VLAN {vlan_id}")
                return True
            else:
                self.logger.error(f"Unknown SNAT operation: {operation_type}")
                return False

        except Exception as e:
            self.logger.error(f"Error handling SNAT operation: {e}")
            return False

    def _handle_dnat_operation(self, operation_type: str, vlan_id: int, 
                              data: Dict[str, Any]) -> bool:
        """Handle DNAT operations.
        
        Args:
            operation_type: create, update, delete
            vlan_id: VLAN identifier
            data: DNAT data
            
        Returns:
            True if successful
        """
        try:
            eip = data.get('eip', '')
            internal_ip = data.get('internal_ip', '')
            
            if operation_type == 'create':
                self.logger.info(f"Creating DNAT rule {eip} -> {internal_ip} on VLAN {vlan_id}")
                return True
            elif operation_type == 'delete':
                self.logger.info(f"Deleting DNAT rule {eip} -> {internal_ip} on VLAN {vlan_id}")
                return True
            else:
                self.logger.error(f"Unknown DNAT operation: {operation_type}")
                return False

        except Exception as e:
            self.logger.error(f"Error handling DNAT operation: {e}")
            return False

    def sync_operations_on_startup(self) -> bool:
        """Sync operations on agent startup by processing missed redo logs.
        
        Returns:
            True if successful
        """
        try:
            last_applied = self.heartbeat_manager.get_last_applied()
            self.logger.info(f"Syncing operations from redo log ID {last_applied}")

            # Get redo logs after last_applied
            redo_logs = get_redo_logs_after_id(last_applied, limit=100)
            
            if not redo_logs:
                self.logger.info("No operations to sync")
                return True

            self.logger.info(f"Found {len(redo_logs)} operations to sync")

            # Process each redo log entry
            for redo_log in redo_logs:
                operation_data = {
                    'operation_type': redo_log.type,
                    'object_type': redo_log.object,
                    'redo_log_id': redo_log.id,
                    'data': redo_log.data
                }
                
                # Extract vlan_id from data if available
                vlan_id = redo_log.data.get('vlan_id', 0)
                operation_data['vlan_id'] = vlan_id

                if not self.handle_operation(operation_data):
                    self.logger.error(f"Failed to sync operation {redo_log.id}")
                    return False

            self.logger.info(f"Successfully synced {len(redo_logs)} operations")
            return True

        except Exception as e:
            self.logger.error(f"Error syncing operations on startup: {e}")
            return False
