"""
HTTP server for v-switch agent.
Provides endpoints for receiving operations from core_service.
"""

import logging
import threading
from typing import Dict, Any, Optional
from flask import Flask, request, jsonify
from v_switch.agent.operation_handler import OperationHandler


class AgentHttpServer:
    """HTTP server for agent to receive operations."""

    def __init__(self, operation_handler: Operation<PERSON><PERSON><PERSON>, host: str = '0.0.0.0', port: int = 8080):
        """Initialize HTTP server.
        
        Args:
            operation_handler: Operation handler instance
            host: Server host
            port: Server port
        """
        self.operation_handler = operation_handler
        self.host = host
        self.port = port
        self.logger = logging.getLogger(__name__)
        
        self.app = Flask(__name__)
        self.app.logger.setLevel(logging.WARNING)  # Reduce Flask logging
        self._setup_routes()
        
        self._server_thread = None
        self._running = False

    def _setup_routes(self):
        """Setup HTTP routes."""
        
        @self.app.route('/health', methods=['GET'])
        def health_check():
            """Health check endpoint."""
            return jsonify({
                'success': True,
                'status': 'healthy',
                'message': 'Agent is running'
            })

        @self.app.route('/api/operation/execute', methods=['POST'])
        def execute_operation():
            """Execute operation endpoint."""
            try:
                if not request.is_json:
                    return jsonify({
                        'success': False,
                        'message': 'Content-Type must be application/json'
                    }), 400

                operation_data = request.get_json()
                
                # Validate required fields
                required_fields = ['operation_type', 'object_type', 'redo_log_id']
                for field in required_fields:
                    if field not in operation_data:
                        return jsonify({
                            'success': False,
                            'message': f'Missing required field: {field}'
                        }), 400

                # Handle the operation
                success = self.operation_handler.handle_operation(operation_data)
                
                if success:
                    return jsonify({
                        'success': True,
                        'message': 'Operation executed successfully'
                    })
                else:
                    return jsonify({
                        'success': False,
                        'message': 'Failed to execute operation'
                    }), 500

            except Exception as e:
                self.logger.error(f"Error executing operation: {e}")
                return jsonify({
                    'success': False,
                    'message': f'Internal server error: {str(e)}'
                }), 500

        @self.app.route('/api/sync', methods=['POST'])
        def sync_operations():
            """Sync operations endpoint."""
            try:
                success = self.operation_handler.sync_operations_on_startup()
                
                if success:
                    return jsonify({
                        'success': True,
                        'message': 'Operations synced successfully'
                    })
                else:
                    return jsonify({
                        'success': False,
                        'message': 'Failed to sync operations'
                    }), 500

            except Exception as e:
                self.logger.error(f"Error syncing operations: {e}")
                return jsonify({
                    'success': False,
                    'message': f'Internal server error: {str(e)}'
                }), 500

        @self.app.route('/api/status', methods=['GET'])
        def get_status():
            """Get agent status endpoint."""
            try:
                return jsonify({
                    'success': True,
                    'status': self.operation_handler.heartbeat_manager.get_current_status(),
                    'last_applied': self.operation_handler.heartbeat_manager.get_last_applied(),
                    'running': self.operation_handler.heartbeat_manager.is_running()
                })

            except Exception as e:
                self.logger.error(f"Error getting status: {e}")
                return jsonify({
                    'success': False,
                    'message': f'Internal server error: {str(e)}'
                }), 500

    def start(self) -> bool:
        """Start HTTP server.
        
        Returns:
            True if successful
        """
        try:
            if self._running:
                self.logger.warning("HTTP server is already running")
                return True

            self._running = True
            self._server_thread = threading.Thread(
                target=self._run_server,
                name="AgentHttpServer",
                daemon=True
            )
            self._server_thread.start()

            self.logger.info(f"Started agent HTTP server on {self.host}:{self.port}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start HTTP server: {e}")
            return False

    def stop(self) -> None:
        """Stop HTTP server."""
        try:
            if not self._running:
                return

            self._running = False
            
            # Note: Flask's development server doesn't have a clean shutdown method
            # In production, you would use a proper WSGI server like gunicorn
            
            self.logger.info("Stopped agent HTTP server")

        except Exception as e:
            self.logger.error(f"Error stopping HTTP server: {e}")

    def _run_server(self) -> None:
        """Run the Flask server."""
        try:
            # Disable Flask's default logging to reduce noise
            import logging as flask_logging
            flask_logging.getLogger('werkzeug').setLevel(flask_logging.WARNING)
            
            self.app.run(
                host=self.host,
                port=self.port,
                debug=False,
                use_reloader=False,
                threaded=True
            )
        except Exception as e:
            self.logger.error(f"Error running HTTP server: {e}")
            self._running = False

    def is_running(self) -> bool:
        """Check if HTTP server is running.
        
        Returns:
            True if running
        """
        return self._running

    def get_endpoint(self) -> str:
        """Get server endpoint.
        
        Returns:
            Server endpoint string
        """
        return f"{self.host}:{self.port}"
