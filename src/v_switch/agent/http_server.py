"""
HTTP server for v-switch agent using FastAPI.
Provides endpoints for receiving operations from core_service.
"""

import logging
import threading
import asyncio
from typing import Dict, Any, Optional
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from v_switch.agent.operation_handler import OperationHandler


class OperationRequest(BaseModel):
    """Request model for operation execution."""
    operation_type: str
    object_type: str
    redo_log_id: int
    vlan_id: Optional[int] = None
    data: Optional[Dict[str, Any]] = None


class SuccessResponse(BaseModel):
    """Success response model."""
    success: bool = True
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class HealthResponse(BaseModel):
    """Health check response model."""
    success: bool = True
    status: str = "healthy"
    message: str = "Agent is running"


class StatusResponse(BaseModel):
    """Status response model."""
    success: bool = True
    status: str
    last_applied: int
    running: bool


class AgentHttpServer:
    """HTTP server for agent to receive operations."""

    def __init__(self, operation_handler: <PERSON><PERSON><PERSON><PERSON>, host: str = '0.0.0.0', port: int = 8080):
        """Initialize HTTP server.

        Args:
            operation_handler: Operation handler instance
            host: Server host
            port: Server port
        """
        self.operation_handler = operation_handler
        self.host = host
        self.port = port
        self.logger = logging.getLogger(__name__)

        self.app = self._create_fastapi_app()
        self._server_thread = None
        self._running = False

    def _create_fastapi_app(self) -> FastAPI:
        """Create FastAPI application with all routes.

        Returns:
            FastAPI application instance
        """
        app = FastAPI(
            title="V-Switch Agent API",
            description="API server for v-switch agent operations",
            version="1.0.0"
        )

        logger = self.logger

        # Health check endpoint
        @app.get("/health", response_model=HealthResponse)
        async def health_check():
            """Health check endpoint."""
            try:
                return HealthResponse(
                    success=True,
                    status="healthy",
                    message="Agent is running"
                )
            except Exception as e:
                logger.error(f"Error in health check: {e}")
                raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

        # Execute operation endpoint
        @app.post("/api/operation/execute", response_model=SuccessResponse)
        async def execute_operation(request: OperationRequest):
            """Execute operation endpoint."""
            try:
                logger.info(f"Received operation: {request.operation_type} {request.object_type}")

                # Convert request to operation data
                operation_data = {
                    'operation_type': request.operation_type,
                    'object_type': request.object_type,
                    'redo_log_id': request.redo_log_id,
                    'vlan_id': request.vlan_id,
                    'data': request.data or {}
                }

                # Handle the operation
                success = self.operation_handler.handle_operation(operation_data)

                if success:
                    return SuccessResponse(
                        success=True,
                        message="Operation executed successfully"
                    )
                else:
                    raise HTTPException(status_code=500, detail="Failed to execute operation")

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error executing operation: {e}")
                raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

        # Sync operations endpoint
        @app.post("/api/sync", response_model=SuccessResponse)
        async def sync_operations():
            """Sync operations endpoint."""
            try:
                logger.info("Starting operation sync")
                success = self.operation_handler.sync_operations_on_startup()

                if success:
                    return SuccessResponse(
                        success=True,
                        message="Operations synced successfully"
                    )
                else:
                    raise HTTPException(status_code=500, detail="Failed to sync operations")

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error syncing operations: {e}")
                raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

        # Get status endpoint
        @app.get("/api/status", response_model=StatusResponse)
        async def get_status():
            """Get agent status endpoint."""
            try:
                return StatusResponse(
                    success=True,
                    status=self.operation_handler.heartbeat_manager.get_current_status(),
                    last_applied=self.operation_handler.heartbeat_manager.get_last_applied(),
                    running=self.operation_handler.heartbeat_manager.is_running()
                )

            except Exception as e:
                logger.error(f"Error getting status: {e}")
                raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

        return app

    def start(self) -> bool:
        """Start HTTP server.

        Returns:
            True if successful
        """
        try:
            if self._running:
                self.logger.warning("HTTP server is already running")
                return True

            self._running = True
            self._server_thread = threading.Thread(
                target=self._run_server,
                name="AgentHttpServer",
                daemon=True
            )
            self._server_thread.start()

            self.logger.info(f"Started agent HTTP server on {self.host}:{self.port}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start HTTP server: {e}")
            return False

    def stop(self) -> None:
        """Stop HTTP server."""
        try:
            if not self._running:
                return

            self._running = False

            # Note: uvicorn server doesn't have a clean shutdown method in this setup
            # In production, you would use a proper server management approach

            self.logger.info("Stopped agent HTTP server")

        except Exception as e:
            self.logger.error(f"Error stopping HTTP server: {e}")

    def _run_server(self) -> None:
        """Run the uvicorn server."""
        try:
            # Configure uvicorn to reduce logging noise
            uvicorn_config = uvicorn.Config(
                app=self.app,
                host=self.host,
                port=self.port,
                log_level="warning",
                access_log=False
            )

            server = uvicorn.Server(uvicorn_config)

            # Run the server in the current thread
            asyncio.run(server.serve())

        except Exception as e:
            self.logger.error(f"Error running HTTP server: {e}")
            self._running = False

    def is_running(self) -> bool:
        """Check if HTTP server is running.

        Returns:
            True if running
        """
        return self._running

    def get_endpoint(self) -> str:
        """Get server endpoint.

        Returns:
            Server endpoint string
        """
        return f"{self.host}:{self.port}"
