"""
配置命令处理器 - 负责比对配置差异并生成执行命令
"""

import logging
import subprocess
from typing import Dict, Any, List, Optional, Tuple
from v_switch.common.models import MonnetConfig, SubnetConfig, SubnetConfigParams, SubnetStatus, TaskData, TaskStatus, EipnetConfig
from v_switch.agent.local_data import LocalDataManager


class CommandProcessor:
    """配置命令处理器"""

    def __init__(self, local_data: LocalDataManager, dry_run: bool = False):
        """初始化配置命令处理器

        Args:
            dry_run: 是否为干运行模式
        """
        self.local_data = local_data
        self.dry_run = dry_run
        self.logger = logging.getLogger(__name__)

    def process_config_changes(self, task: TaskData,
                               remote_config: SubnetConfigParams,
                               local_config: SubnetConfigParams) -> None:
        """处理配置变更

        Args:
            task: 任务数据
            remote_config: 远程配置
            local_config: 本地配置

        Returns:
            True if successful
        """
        if not remote_config and not local_config:
            if task: task.status = TaskStatus.ERROR
            self.logger.error("remote_config and local_config cannot both be None")
            return

        if not remote_config or remote_config.status == SubnetStatus.DELETION:
            # 如果远程配置不存在或者处于删除状态，执行全量删除
            del_config = remote_config or local_config
            if self._delete_full_by_config(task, del_config):
                if task:
                    task.status = TaskStatus.COMPLETE
                if del_config:
                    self.local_data.delete_local_config(del_config.to_dict())
            return

        if not local_config and remote_config.status != SubnetStatus.DELETION:
            # 只有远程配置，生成全量创建命令
            if self._create_full_by_config(task, remote_config):
                if task:
                    task.status = TaskStatus.COMPLETE
                self.local_data.update_local_config(remote_config.to_dict())
            return

        if local_config and remote_config.status != SubnetStatus.DELETION:
            # 两个配置都存在，比对差异生成更新命令
            if self._process_config_diff(task, remote_config, local_config):
                if task:
                    task.status = TaskStatus.COMPLETE
                self.local_data.update_local_config(remote_config.to_dict())
            return

    def _create_full_by_config(self, task: TaskData, config: SubnetConfigParams):
        """创建所有配置

        Args:
            task: 任务数据
            config: 配置数据
        """
        if not self._create_subnet(task, config):
            return False
        if not self._create_monnet(task, config):
            return False
        if not self._create_eipnet(task, config):
            return False
        return True

    def _delete_full_by_config(self, task: TaskData, config: SubnetConfigParams) -> bool:
        """删除所有配置

        Args:
            task: 任务数据
            config: 配置数据
        """
        vlan_id = config.vlan_id
        namespace = self._get_namespace(vlan_id)
        # 清除 nftables 规则
        self._exec_single_command(task,
                                     f"ip netns exec {namespace} nft flush ruleset || true")

        # 清除设备
        if not self._del_internal_port(task, namespace, "br-eip", "eip", vlan_id):
            return False
        if not self._del_internal_port(task, namespace, "br-mon", "mon", vlan_id):
            return False
        if not self._del_internal_port(task, namespace, "br-vlan", "lan", vlan_id):
            return False
        # 清除命名空间
        if not self._delete_netns(task, namespace):
            return False
        return True

    # 创建子网
    def _create_subnet(self, task: TaskData, config: SubnetConfigParams) -> bool:
        """
        创建子网

        Args:
            task: 任务数据
            config: 配置数据
        """
        sub_conf = config.subnet
        if not sub_conf:
            # 没有子网配置
            return True
        vlan_id = config.vlan_id
        namespace = self._get_namespace(vlan_id)
        if not self._create_netns(task, namespace):
            return False
        # 创建设备
        gateway_ip = sub_conf.gateway_ip
        mask = sub_conf.mask
        if not self._add_internal_port(task, namespace, "br-vlan", "lan", vlan_id, gateway_ip, mask, vlan_id):
            return False
        self._exec_single_command(None, f"ip netns exec {namespace} ip link set lo up")
        # 创建 nft 表
        table_name = self._get_nft_table_name(vlan_id)
        if not self._add_nft_table(task, namespace, table_name):
            return False
        post_chain = "postrouting_sub"
        if not self._add_nft_chain(task, namespace, table_name, post_chain, "postrouting", 100):
            return False
        # 创建 nft 规则
        mask = sub_conf.mask
        pre_ip = sub_conf.pre_ip
        if not self._add_nft_rule(task, namespace, table_name, post_chain,
                                  f"ip saddr {pre_ip}.0/{mask} ip daddr {pre_ip}.0/{mask} counter snat to {gateway_ip}",
                                  comment=f"subnet_addr_{pre_ip}_{mask}_snat_to_{gateway_ip}"):
            return False
        return True

    def _delete_subnet(self, task: TaskData, config: SubnetConfigParams) -> bool:
        """删除子网配置"""
        if not config.subnet:
            return True

        vlan_id = config.vlan_id
        namespace = self._get_namespace(vlan_id)
        # 删除 nft 链
        table_name = self._get_nft_table_name(vlan_id)
        post_chain = "postrouting_sub"
        if not self._del_nft_chain(task, namespace, table_name, post_chain):
            return False
        # 删除子网设备
        if not self._del_internal_port(task, namespace, "br-vlan", "lan", vlan_id):
            return False
        return True

    def _create_monnet(self, task: TaskData, config: SubnetConfigParams) -> bool:
        """创建监控网络

        Args:
            task: 任务数据
            config: 配置数据
        """
        mon_conf = config.monnet
        if not mon_conf:
            # 没有监控网络配置
            return True
        vlan_id = config.vlan_id
        mon_ip = mon_conf.mon_ip
        namespace = self._get_namespace(vlan_id)
        # 创建监控设备
        if not self._add_internal_port(task, namespace, "br-mon", "mon", vlan_id, mon_ip):
            return False
        # 创建 nftables
        table_name = self._get_nft_table_name(vlan_id)
        if not self._add_nft_table(task, namespace, table_name):
            return False
        # 创建 PREROUTING 链
        pre_chain = "prerouting_mon"
        if not self._add_nft_chain(task, namespace, table_name, pre_chain, "prerouting", -98):
            return False
        # 创建 POSTROUTING 链
        post_chain = "postrouting_mon"
        if not self._add_nft_chain(task, namespace, table_name, post_chain, "postrouting", 102):
            return False
        # 创建 nftables 规则
        mon_ip = mon_conf.mon_ip
        mon_m_ip = mon_conf.mon_m_ip
        mon_m_port = mon_conf.mon_m_ports
        ext_m_ports = mon_conf.ext_m_ports
        if not self._add_nft_rule(task, namespace, table_name, pre_chain,
                                  f"ip daddr *************** tcp dport 443 counter dnat to {mon_m_ip}:{mon_m_port}",
                                  f"monnet_dnat_to_{mon_m_ip}_{mon_m_port}"):
            return False
        if not self._add_nft_rule(task, namespace, table_name, post_chain,
                                  f"ip daddr {mon_m_ip} tcp dport {mon_m_port} counter snat to {mon_ip}",
                                  f"monnet_snat_to_{mon_ip}"):
            return False
        # 添加路由
        gateway = mon_conf.mon_gateway_ip
        device = self._get_device_name("mon", "ns", vlan_id)
        # route_rule = f"{mon_m_ip} via {gateway} dev {device}"
        # if not self._execute_check_command(f"ip netns exec {namespace} ip route show | grep '{route_rule}'"):
        #     if not self._execute_single_command(task, f"ip netns exec {namespace} ip route add {route_rule}"):
        #         return False
        if not self._add_ip_route(task, namespace, device, mon_m_ip, gateway):
            return False
        return True

    def _delete_monnet(self, task: TaskData, config: SubnetConfigParams) -> bool:
        """删除监控网络

        Args:
            task: 任务数据
            config: 配置数据
        """
        mon_conf = config.monnet
        if not mon_conf:
            # 没有监控网络配置
            return True
        vlan_id = config.vlan_id
        namespace = self._get_namespace(vlan_id)
        # 删除链
        table_name = self._get_nft_table_name(vlan_id)
        pre_chain = "prerouting_mon"
        post_chain = "postrouting_mon"
        if not self._del_nft_chain(task, namespace, table_name, pre_chain):
            return False
        if not self._del_nft_chain(task, namespace, table_name, post_chain):
            return False
        # 删除设备
        if not self._del_internal_port(task, namespace, "br-mon", "mon", vlan_id):
            return False
        # 删除路由
        mon_m_ip = mon_conf.mon_m_ip
        gateway = mon_conf.mon_gateway_ip
        device = self._get_device_name("mon", "ns", vlan_id)
        if not self._del_ip_route(task, namespace, device, mon_m_ip, gateway):
            return False
        return True

    def _create_eipnet(self, task: TaskData, config: SubnetConfigParams) -> bool:
        """创建 EIP 网络配置"""
        eipnet_config = config.eipnet
        if not eipnet_config or (not eipnet_config.mounts and not eipnet_config.snat and not eipnet_config.dnat):
            return True

        vlan_id = config.vlan_id
        namespace = self._get_namespace(vlan_id)
        device = self._get_device_name('eip', 'ns', vlan_id)
        gateway_ip = eipnet_config.gateway_ip

        # 创建 EIP 设备（如果已存在则只设置IP）
        if not self._add_internal_port(task, namespace, "br-eip", "eip", vlan_id):
            return False
        # 创建 nft 表和链
        table_name = self._get_nft_table_name(vlan_id)
        if not self._add_nft_table(task, namespace, table_name):
            return False
        pre_chain_name = "prerouting_eip"
        if not self._add_nft_chain(task, namespace, table_name, pre_chain_name, "prerouting", -99):
            return False
        post_chain_name = "postrouting_eip"
        if not self._add_nft_chain(task, namespace, table_name, post_chain_name, "postrouting", 101):
            return False

        # 处理每个 EIP 挂载
        for i in range(len(eipnet_config.mounts)):
            mount = eipnet_config.mounts[i]
            eip = mount.eip
            internal_ip = mount.internal_ip

            # 创建 EIP 设备（如果已存在则只设置IP）
            if not self._add_internal_port(task, namespace, "br-eip", "eip", vlan_id, eip):
                return False
            # 添加默认路由
            if not self._add_ip_route(task, namespace, device, "default", gateway_ip):
                return False
            # 添加 nft 规则
            if not self._add_nft_rule(task, namespace, table_name, pre_chain_name,
                                      f"ip daddr {eip} counter dnat to {internal_ip}",
                                      comment=f"eip_{eip}_dnat_to_{internal_ip}"):
                return False
            exclude_ips = "{10.0.0.0/8, **********/12, ***********/24, ***********/24}"
            if not self._add_nft_rule(task, namespace, table_name, post_chain_name,
                                      f"ip saddr {internal_ip} ip daddr != {exclude_ips} snat to {eip}",
                                      comment=f"eip_{internal_ip}_snat_to_{eip}"):
                return False
            
            # 处理 tc 限速
            width = eipnet_config.band_width
            if width and mount.rate:
                # 创建TC根队列
                if not self._add_tc_qdisc(task, namespace, device, handle="1:",
                                          parent="root", type="htb", options="default 10"):
                    return False
                if not self._add_tc_class(task, namespace, device, parent="1:", handle="1:1",
                                          type="htb", rate=width, ceil=width):
                    return False
                # 创建TC默认类
                if not self._add_tc_class(task, namespace, device, parent="1:1", handle="1:10",
                                          type="htb", rate=width, ceil=width):
                    return False
                if not self._add_tc_qdisc(task, namespace, device, handle="ffff:", type="ingress"):
                    return False
                minor = i + 100
                rate = f"{mount.rate}" if mount.rate.endswith("mbit") else f"{mount.rate}mbit"
                if not mount.ceil:
                    ceil = rate
                else:
                    ceil = f"{mount.ceil}" if mount.ceil.endswith("mbit") else f"{mount.ceil}mbit"
                    
                if not self._add_tc_class(task, namespace, device, parent="1:1", handle=f"1:{minor}", 
                                          type="htb", rate=rate, ceil=ceil):
                    return False
                if not self._add_tc_qdisc(task, namespace, device, handle=f"{minor}:", 
                                          parent=f"1:{minor}", type="pfifo", options="limit 1000" ):
                    return False
                # tc filter add dev ${NIC_NAME} protocol ip parent 1: prio 10 u32 match ip src ${TC_EIP} flowid 1:1${LIMIT_mb}
                if not self._add_tc_filter(task, namespace, device, f"ip src {eip}", parent="1:", prio=10, flowid=f"1:{minor}"):
                    return False
                # tc filter add dev ${NIC_NAME} parent ffff: protocol ip prio 20 u32 match ip dst ${TC_EIP}/32 police rate ${LIMIT_mb}mbit burst ${LIMIT_mb}mbit drop
                option = f"police rate {rate} burst {rate} drop"
                if not self._add_tc_filter(task, namespace, device, f"ip dst {eip}", parent="ffff:", option=option, prio=20):
                    return False
                
        # 处理 SNAT
        for i in range(len(eipnet_config.snat)):
            snat = eipnet_config.snat[i]
            if not self._add_snat(task, vlan_id, snat.eip, gateway_ip, snat.gateway_ip, snat.mask):
                return False
            
        # 处理 DNAT
        for i in range(len(eipnet_config.dnat)):
            dnat = eipnet_config.dnat[i]
            if not self._add_dnat(task, vlan_id, dnat.eip, gateway_ip, dnat.type, dnat.dport, dnat.internal_ip, dnat.port):
                return False
            
        return True
    

    def _delete_eipnet(self, task: TaskData, config: SubnetConfigParams) -> bool:
        """删除 EIP 网络配置"""
        eipnet_config = config.eipnet
        if not eipnet_config:
            return True

        vlan_id = config.vlan_id
        namespace = self._get_namespace(vlan_id)

        # # 删除链
        table_name = self._get_nft_table_name(vlan_id)
        gateway_ip = eipnet_config.gateway_ip

        # 如果没有 EIP 挂载, 则删除默认路由和 EIP 设备
        # 删除默认路由
        device = self._get_device_name('eip', 'ns', vlan_id)
        if not self._del_ip_route(task, namespace, device, "default", gateway_ip):
            return False
        # 删除nft链
        pre_chain = "prerouting_eip"
        post_chain = "postrouting_eip"
        if not self._del_nft_chain(task, namespace, table_name, pre_chain):
            return False
        if not self._del_nft_chain(task, namespace, table_name, post_chain):
            return False
        # 清理 tc 规则
        if not self._clear_tc(task, namespace, device):
            return False
        # 删除设备
        return self._del_internal_port(task, namespace, "br-eip", "eip", vlan_id)
    
    def _add_snat(self, task: TaskData, vlan_id: str, eip: str, eip_gw_ip: str, subnet: str, mask: int = 24) -> bool:
        """添加 SNAT 规则
        
        Args:
            task: 任务数据
            vlan_id: VLAN ID
            eip: EIP 地址
            subnet: 内网子网地址
            mask: 子网掩码
        """
        namespace = self._get_namespace(vlan_id)
        table_name = self._get_nft_table_name(vlan_id)
        device = self._get_device_name('eip', 'ns', vlan_id)
           # 创建 EIP 设备（如果已存在则只设置IP）
        if not self._add_internal_port(task, namespace, "br-eip", "eip", vlan_id, eip):
            return False
        # 添加默认路由
        if not self._add_ip_route(task, namespace, device, "default", eip_gw_ip):
            return False
        if not self._add_nft_table(task, namespace, table_name):
            return False
        post_chain = "postrouting_eip"
        if not self._add_nft_chain(task, namespace, table_name, post_chain, "postrouting", 101):
            return False
        # 添加 SNAT 规则
        # ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat postrouting ip saddr ${ZW_SUB_IP} snat to ${EIP}
        space = f"ip netns exec {namespace}"
        if not self._add_nft_rule(task, namespace, table_name, post_chain,
                                  f"ip saddr {subnet}/{mask} counter snat to {eip}",
                                  comment=f"eip_{subnet}_{mask}_snat_to_{eip}"):
            return False
        return True
    
    def _del_snat(self, task: TaskData, vlan_id: str, eip: str, subnet: str, mask: int = 24) -> bool:
        """删除 SNAT 规则
        
        Args:
            task: 任务数据
            vlan_id: VLAN ID
            eip: EIP 地址
            subnet: 内网子网地址
            mask: 子网掩码
        """
        namespace = self._get_namespace(vlan_id)
        table_name = self._get_nft_table_name(vlan_id)
        post_chain = "postrouting_eip"
        return self._del_nft_rule(task, namespace, table_name, post_chain, f"eip_{subnet}_{mask}_snat_to_{eip}")
    
    def _add_dnat(self, task: TaskData, vlan_id: str, eip: str, eip_gw_ip:str, 
                  type: str, dport: int, internal_ip: str, port: int) -> bool:
        """添加 DNAT 规则

        Args:
            task: 任务数据
            vlan_id: VLAN ID
            eip: EIP 地址
            type: 协议类型, tcp 或 udp
            internal_ip: 内网 IP 地址
            dport: 外部端口, 如 8000
            port: 端口映射, 如 80
        
        """
        namespace = self._get_namespace(vlan_id)
        table_name = self._get_nft_table_name(vlan_id)
        if not self._add_nft_table(task, namespace, table_name):
            return False
        pre_chain = "prerouting_eip"
        if not self._add_nft_chain(task, namespace, table_name, pre_chain, "prerouting", -99):
            return False
        device = self._get_device_name('eip', 'ns', vlan_id)
        # 创建 EIP 设备（如果已存在则只设置IP）
        if not self._add_internal_port(task, namespace, "br-eip", "eip", vlan_id, eip):
            return False
        # 添加默认路由
        if not self._add_ip_route(task, namespace, device, "default", eip_gw_ip):
            return False
        # type 转换成小写
        return self._add_nft_rule(task, namespace, table_name, pre_chain,
                                  f"ip daddr {eip} {type.lower()} dport {dport} dnat to {internal_ip}:{port}",
                                  comment=f"eip_{eip}_{dport}_dnat_to_{internal_ip}_{port}")
    
    def _del_dnat(self, task: TaskData, vlan_id: str, eip: str, type: str, dport: int, internal_ip: str, port: int) -> bool:
        """删除 DNAT 规则

        Args:
            task: 任务数据
            vlan_id: VLAN ID
            eip: EIP 地址
            type: 协议类型, tcp 或 udp
            internal_ip: 内网 IP 地址
            dport: 外部端口, 如 8000
            port: 端口映射, 如 80
        
        """
        namespace = self._get_namespace(vlan_id)
        table_name = self._get_nft_table_name(vlan_id)
        pre_chain = "prerouting_eip"
        return self._del_nft_rule(task, namespace, table_name, pre_chain, f"eip_{eip}_{dport}_dnat_to_{internal_ip}_{port}")

    # 创建命名空间
    def _create_netns(self, task: TaskData, namespace: str) -> bool:
        """创建命名空间

        Args:
            task: 任务数据
            namespace: 命名空间名称

        """
        if not self._exec_check_command(f"ip netns list | grep -w {namespace}"):
            # 不存在则创建
            if not self._exec_single_command(task, f"ip netns add {namespace}"):
                return False
        return True

    # 删除命名空间
    def _delete_netns(self, task: TaskData, namespace: str) -> bool:
        """删除命名空间

        Args:
            task: 任务数据
            namespace: 命名空间名称

        """
        if self._exec_check_command(f"ip netns list | grep -w {namespace}"):
            if not self._exec_single_command(task, f"ip netns delete {namespace}"):
                return False
        return True

    def _add_internal_port(self, task: TaskData, namespace: str, bridge: str,
                           prefix: str, vlan_id: int, ip: str=None, mask: int = 24, tag: int = 0) -> bool:
        """创建 ovs 内部端口

        Args:
            task: 任务数据
            namespace: 命名空间名称
            bridge: 网桥名称
            prefix: 设备名称前缀
            vlan_id: VLAN ID
            ip_address: IP 地址, 可选

        """
        space = f"ip netns exec {namespace}"
        device = self._get_device_name(prefix, "ns", vlan_id)
        if not self._exec_check_command(f"ovs-vsctl list-ports {bridge} | grep {device}"):
            # 创建设备并连接到网桥
            tag_opt = f"tag={tag}" if tag else ""
            if not self._exec_single_command(task,
                                                f"ovs-vsctl add-port {bridge} {device} {tag_opt} -- set interface {device} type=internal"):
                return False
            
        # 基于udevadm settle等待设备创建完成
        if not self._exec_single_command(task, f"udevadm settle --timeout 3"):
            return False
        if self._exec_check_command(f"ip link show dev {device}") and not self._exec_check_command(f"{space} ip link show dev {device}"):
            # 移动设备到命名空间
            if not self._exec_single_command(task, f"ip link set {device} netns {namespace}"):
                return False
            self._exec_single_command(task, f"{space} ip link set {device} up")

        if ip and not self._exec_check_command(
                f"{space} ip addr show dev {device} | grep -w {ip}"):
            # 设备添加 IP
            if not self._exec_single_command(task,
                                                f"{space} ip addr add {ip}/{mask} dev {device}"):
                return False
        return True

    def _del_internal_port(self, task: TaskData, namespace: str, bridge: str, prefix: str, vlan_id: int) -> bool:
        """删除 ovs 内部端口

        Args:
            task: 任务数据
            namespace: 命名空间名称
            bridge: 网桥名称
            prefix: 设备名称前缀
            vlan_id: VLAN ID

        """
        space = f"ip netns exec {namespace}"
        device = self._get_device_name(prefix, "ns", vlan_id)
        if self._exec_check_command(f"{space} ip link show {device}"):
            # 关闭设备
            if not self._exec_single_command(task, f"{space} ip link set {device} down"):
                return False
        if self._exec_check_command(f"ovs-vsctl list-ports {bridge} | grep {device}"):
            # 删除端口
            if not self._exec_single_command(task, f"ovs-vsctl --if-exists del-port {bridge} {device}"):
                return False
        return True

    def _add_ip_route(self, task: TaskData, namespace: str, device: str,
                      target: str, gateway: str, src: str = None, metric: int = 0) -> bool:
        """添加路由
        
        Args:
            task: 任务数据
            namespace: 命名空间名称
            device: 设备名称
            target: 目标网络
            gateway: 网关
            src: 源地址
            metric: 路由优先级
        """
        space = f"ip netns exec {namespace}"

        src_opt = f" src {src}" if src else ""
        metric_opt = f" metric {metric}" if metric else ""
        dev_opt = f" dev {device}" if device else ""

        route_rule = f"{target} via {gateway}{dev_opt}{src_opt}{metric_opt}"
        if not self._exec_check_command(f"{space} ip route show | grep -w '{route_rule}'"):
            cmd = f"{space} ip route add {route_rule}"
            if not self._exec_single_command(task, cmd):
                return False
        return True

    def _del_ip_route(self, task: TaskData, namespace: str, device: str,
                      target: str, gateway: str, src: str = None, metric: int = 0) -> bool:
        """删除路由
        
        Args:
            task: 任务数据
            namespace: 命名空间名称
            target: 目标网络
            gateway: 网关
            src: 源地址
        """
        space = f"ip netns exec {namespace}"

        src_opt = f" src {src}" if src else ""
        metric_opt = f" metric {metric}" if metric else ""
        dev_opt = f" dev {device}" if device else ""

        route_rule = f"{target} via {gateway}{dev_opt}{src_opt}{metric_opt}"
        if not self._exec_check_command(f"{space} ip route show | grep -w '{route_rule}'"):
            return True
        cmd = f"{space} ip route del {route_rule}"
        if not self._exec_single_command(task, cmd):
            return False
        return True

    # 创建 nft 表
    def _add_nft_table(self, task: TaskData, namespace: str, table_name: str) -> bool:
        """创建 nft 表

        Args:
            task: 任务数据
            namespace: 命名空间名称
            table_name: nft 表名称

        """
        space = f"ip netns exec {namespace}"
        if not self._exec_check_command(f"{space} nft list table ip {table_name}"):
            cmd = f"{space} nft add table ip {table_name}"
            if not self._exec_single_command(task, cmd):
                return False
        return True

    # 删除 nft 表
    def _del_nft_table(self, task: TaskData, namespace: str, table_name: str) -> bool:
        """删除 nft 表

        Args:
            task: 任务数据
            namespace: 命名空间名称
            table_name: nft 表名称

        """
        space = f"ip netns exec {namespace}"
        if self._exec_check_command(f"{space} nft list table ip {table_name}"):
            cmd = f"{space} nft delete table ip {table_name}"
            if not self._exec_single_command(task, cmd):
                return False
        return True

    # 创建 nft 链
    def _add_nft_chain(self, task: TaskData, namespace: str, 
                          table_name: str, chain: str, hook: str, priority: Optional[int] = None) -> bool:
        """创建 nft 链

        Args:
            task: 任务数据
            namespace: 命名空间名称
            table_name: nft 表名称
            chain: nft 链名称
            hook: nft 链钩子 (prerouting | postrouting)
            priority: nft 链优先级, 选填; hook 为 prerouting 时, 默认为 -100, hook 为 postrouting 时, 默认为 100
        """
        space = f"ip netns exec {namespace}"
        if priority is None:
            if hook == "prerouting":
                priority = -100
            elif hook == "postrouting":
                priority = 100
            else:
                self.logger.error(f"Invalid hook {hook} for nft chain")
                return False
        if not self._exec_check_command(f"{space} nft list chain ip {table_name} {chain}"):
            cmd = f"{space} nft add chain ip {table_name} {chain} {{ type nat hook {hook} priority {priority} \\; }}"
            if not self._exec_single_command(task, cmd):
                return False
        return True

    # 删除 nft 链
    def _del_nft_chain(self, task: TaskData, namespace: str, table_name: str, chain: str) -> bool:
        """删除指定的 nft 链

        Args:
            task: 任务数据
            namespace: 命名空间名称
            table_name: nft 表名称
            chain: nft 链名称

        """
        space = f"ip netns exec {namespace}"
        if self._exec_check_command(f"{space} nft list chain ip {table_name} {chain}"):
            cmd = f"{space} nft delete chain ip {table_name} {chain}"
            if not self._exec_single_command(task, cmd):
                return False
        return True

    # 添加 nft 规则
    def _add_nft_rule(self, task: TaskData, namespace: str, 
                      table_name: str, chain: str, rule: str, comment: str) -> bool:
        """添加一条 nft 规则

        Args:
            task: 任务数据
            namespace: 命名空间名称
            table_name: nft 表名称
            chain: nft 链名称
            rule: nft 规则
            comment: nft 规则注释, 用于匹配规则, 避免重复
        """
        space = f"ip netns exec {namespace}"
        if not self._exec_check_command(f"{space} nft list ruleset | grep 'comment \"{comment}\"'"):
            cmd = f"{space} nft add rule ip {table_name} {chain} {rule} comment \"{comment}\""
            if not self._exec_single_command(task, cmd):
                return False
        return True

    # 删除 nft 规则
    def _del_nft_rule(self, task: TaskData, namespace: str, 
                      table_name: str, chain: str, comment: str) -> bool:
        """删除一条 nft 规则

        Args:
            task: 任务数据
            namespace: 命名空间名称
            table_name: nft 表名称
            chain: nft 链名称
            rule: nft 规则
            comment: nft 规则注释, 用于匹配规则, 必须要和添加时的注释完全一致
        """
        space = f"ip netns exec {namespace}"
        success, handle, _ = self._exec_simple_command(
            f"{space} nft -a list chain ip {table_name} {chain} | grep 'comment \"{comment}\"' | awk '{{print $NF}}'")
        if success:
            cmd = f"{space} nft delete rule ip {table_name} {chain} handle {handle}"
            if not self._exec_single_command(task, cmd):
                return False
        return True
    
    def _clear_tc(self, task: TaskData, namespace : str, device: str) -> bool:
        # 清理tc规则
        cmd = f"ip netns exec {namespace} tc qdisc del dev {device} root || true"
        if not self._exec_single_command(task, cmd):
            return True
        cmd = f"ip netns exec {namespace} tc qdisc del dev {device} ingress || true"
        if not self._exec_single_command(task, cmd):
            return True
        return True
    
    
    def _add_tc_qdisc(self, task: TaskData, namespace: str, device: str, 
                      handle: str, parent: str = None, type: str="htb", options: str = None) -> bool:
        """添加 root qdisc

        tc qdisc add dev DEVICE [ root | parent CLASSID ] [ handle HANDLE ] QDISC_TYPE [ OPTIONS ]
        Args:
            task: 任务数据
            namespace: 命名空间名称
            device: 设备名称
            handle: qdisc 句柄, 一般为 1:
            options: 特定于你所选 QDISC_TYPE 的参数。例如，对于 htb，你可以指定 default 参数；对于 pfifo，你可以指定 limit 参数。

            常见的 Qdisc 类型 (QDISC_TYPE)
              htb (Hierarchical Token Bucket - 层级令牌桶)
              用途: 最常用、最强大的Qdisc，用于创建复杂的、层级化的带宽限制和优先级策略。
              特点: 可以定义“保证速率”（rate）和“最大速率”（ceil），允许类之间借用带宽，非常灵活。
              常见Options: default <minor-id> 指定未分类流量默认进入哪个子类。
              示例: tc qdisc add dev eth0 root handle 1: htb default 10
              pfifo_fast (Priority FIFO Fast)
              用途: Linux系统默认的Qdisc。
              特点: 内部有3个FIFO队列（称为band 0, 1, 2），根据数据包的TOS（Type of Service）字段来决定放入哪个队列，band 0的优先级最高。简单、开销小。
              示例: tc qdisc add dev eth0 root handle 1: pfifo_fast
              sfq (Stochastic Fairness Queueing - 随机公平队列)
              用途: 实现数据流之间的公平性。
              特点: 它会尝试确保每个数据流（基于源/目的IP和端口的哈希）都能获得公平的发送机会，防止某个大流量下载任务饿死其他所有连接（如SSH、网页浏览）。
              常见Options: perturb <seconds> 指定多久重新计算一次哈希，防止哈希碰撞导致的持续不公平。
              示例: tc qdisc add dev eth0 root handle 1: sfq perturb 10
              fq_codel (Fair Queueing with Controlled Delay)
              用途: 现代Linux发行版中广泛使用的默认Qdisc，旨在解决“缓冲膨胀（Bufferbloat）”问题。
              特点: 它结合了公平队列（FQ）和CoDel算法，不仅保证了流之间的公平性，还能通过主动管理队列延迟来防止网络卡顿，极大地改善了交互式应用的体验（如SSH、在线游戏、VoIP）。
              示例: tc qdisc add dev eth0 root handle 1: fq_codel
              ingress
              用途: 一个特殊的Qdisc，用于**入口流量（incoming traffic）**控制。
              特点: 它本身不进行任何排队或调度。它的唯一作用是作为一个挂载点，让你可以在上面附加tc filter，并使用police动作来对入口流量进行强制速率限制（丢弃超额流量）。
              示例: tc qdisc add dev eth0 handle ffff: ingress
        """
        space = f"ip netns exec {namespace}"

        handle_opt = f" handle {handle}"
        if type == "ingress":
            parent_opt = ""
        else:
            parent_opt = f" parent {parent}" if parent != "root" else " root"
        option_opt = f" {options}" if options else ""

        query = f"{space} tc qdisc show dev {device} | grep -w 'qdisc {type} {handle}{parent_opt}'"
        if self._exec_check_command(query):
            return True
        cmd = f"{space} tc qdisc add dev {device}{parent_opt}{handle_opt} {type}{option_opt}"
        return self._exec_single_command(task, cmd)
    

    def _del_tc_qdisc(self, task: TaskData, namespace: str, device: str,
                      type: str, handle: str, parent: str = None) -> bool:
        """删除 root qdisc
        Args:
            task: 任务数据
            namespace: 命名空间名称
            device: 设备名称
        """
        space = f"ip netns exec {namespace}"

        handle_opt = f"handle {handle}:"
        if type == "ingress":
            parent_opt = ""
        else:
            parent_opt = f"parent {parent}" if parent != "root" else "root"

        query = f"{space} tc qdisc show dev {device} | grep -w 'qdisc {type} {handle}: {parent_opt}'"
        if not self._exec_check_command(query):
            return True
        cmd = f"{space} tc qdisc del dev {device} {parent_opt} {handle_opt} {type}"
        return self._exec_single_command(task, cmd)
    
    def _add_tc_class(self, task: TaskData, namespace: str, device: str, 
                      parent: str, handle: str, type: str,
                      rate: str, ceil: str = None) -> bool:
        """添加 class
            
            tc class add dev DEV parent QDISC-ID classid CLASS-ID QDISC [ QDISC-SPECIFIC-PARAMETERS ]

        Args:
            task: 任务数据
            namespace: 命名空间名称
            device: 设备名称
            parent: 父 class id
            handle: class id
            type: qdisc 类型
            rate: 分配给该类别的保证带宽。这是该类别在任何情况下都能获得的最低速率。
            ceil: (可选): 分配给该类别的最高带宽。当父节点有空闲带宽时，该类别可以借用带宽，但不能超过这个上限。
        """
        space = f"ip netns exec {namespace}"
        if self._exec_check_command(f"{space} tc class show dev {device} | grep -w 'class {type} {handle}'"):
            return True
        parent_opt = f"parent {parent}" if parent else ""
        handle_opt = f"classid {handle}"
        rate_opt = f"rate {rate}" if rate.endswith("mbit") else f"rate {rate}mbit"
        if ceil:
            ceil_opt = f"ceil {ceil}" if ceil.endswith("mbit") else f"ceil {ceil}mbit"
        else:
            ceil_opt = ""
        cmd = f"{space} tc class add dev {device} {parent_opt} {handle_opt} {type} {rate_opt} {ceil_opt}"
        return self._exec_single_command(task, cmd)
    
    def _del_tc_class(self, task: TaskData, namespace: str, device: str, 
                      handle: str, type: str = "htb") -> bool:
        """删除 class

        Args:
            task: 任务数据
            namespace: 命名空间名称
            device: 设备名称
            handle: class id
            type: qdisc 类型
        """
        space = f"ip netns exec {namespace}"
        if not self._exec_check_command(f"{space} tc class show dev {device} | grep -w 'class {type} {handle}'"):
            return True
        cmd = f"{space} tc class del dev {device} classid {handle}"
        return self._exec_single_command(task, cmd)
    
    def _add_tc_filter(self, task: TaskData, namespace: str, device: str,
                       match: str, parent: str, protocol: str = "ip",
                       prio: int = 10, type: str = "u32", option:str="", flowid: str = None) -> bool:
        """添加 filter

        tc filter add dev DEV parent QDISC-ID protocol PROTO prio PRIORITY FILTER_TYPE [ FILTER_OPTIONS ] flowid CLASS_ID
        Args:
            task: 任务数据
            namespace: 命名空间名称
            device: 设备名称
            match: 匹配条件, 如 ip src ***********
            handle: filter 句柄, 如 10:
            parent: 父 class id
            protocol: 协议, 如 ip
            prio: 优先级, 默认10
            type: filter 类型, 如 u32
            flowid: 流量流向, 如 1:10
        """
        space = f"ip netns exec {namespace}"
        if self._exec_check_command(f"{space} tc -p filter show dev {device} | grep -wi 'match {match}'"):
            return True

        protocol_opt = f"protocol {protocol}"
        parent_opt = f"parent {parent}"
        prio_opt = f"prio {prio}"
        match = f"match {match}"
        action = f"flowid {flowid}" if flowid else ""
        cmd = f"{space} tc filter add dev {device} {parent_opt} {protocol_opt} {prio_opt} {type} {match} {option} {action}"
        return self._exec_single_command(task, cmd)
    

    def _del_tc_filter(self, task: TaskData, namespace: str, device: str,
                       match: str, parent: str, protocol: str = "ip",
                       prio: int = 10, type: str = "u32") -> bool:
        """删除 filter

        Args:
            task: 任务数据
            namespace: 命名空间名称
            device: 设备名称
            match: 匹配条件, 如 ip src ***********
            parent: 父 class id
            protocol: 协议, 如 ip
            prio: 优先级, 默认10
            type: filter 类型, 如 u32
        """
        space = f"ip netns exec {namespace}"
        if self._exec_check_command(f"{space} tc -p filter show dev {device} | grep -wi 'match {match}'"):
            return True

        protocol_opt = f"protocol {protocol}"
        parent_opt = f"parent {parent}"
        prio_opt = f"pref {prio}"
        match_opt = f"match {match}"
        base = f"tc filter del dev {device} {protocol_opt} {parent_opt}"
        cmd = f"{space} {base} {prio_opt} {type} {match_opt}"
        # cmd = f"{space} tc filter del dev {device} {parent_opt} {protocol_opt} {prio_opt} {type} {match} {action}"
        return self._exec_single_command(task, cmd)

    def _exec_check_command(self, cmd: str) -> bool:
        """执行单个命令

        Args:
            cmd: 要执行的命令
        Returns:
            True if command executed successfully
        """
        success = False
        try:
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=10
            )

            success = result.returncode == 0

        except Exception as e:
            success = False
        message = result.stderr.strip()
        self.logger.info(f"Executing check command: {cmd} return code: {result.returncode}, message: {message}")
        return success

    def _exec_single_command(self, task: TaskData, cmd: str) -> bool:
        """执行单个命令

        Args:
            task: 任务数据
            cmd: 要执行的命令

        Returns:
            Tuple of (success, output, error)
        """
        try:
            if task:
                task.append_cmd(cmd)

            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=5
            )

            success = result.returncode == 0
            output = result.stdout.strip()
            error = result.stderr.strip()

            if success:
                self.logger.info(f"Command succeeded: {cmd} \nOutput: {output}")
                return True
            else:
                self.logger.error(f"Command failed: {cmd} \nError: {error}")
                if task:
                    task.status = TaskStatus.ERROR
                    task.message = f"cmd [{cmd}] execute error: {error}"
                return False
        except subprocess.TimeoutExpired:
            self.logger.error(f"Command timeout: {cmd}")
            return False
        except Exception as e:
            self.logger.error(f"Command [{cmd}] execution error: {e}")
            return False

    def _exec_simple_command(self, cmd: str) -> Tuple[bool, str, str]:
        """执行单个命令

        Args:
            task: 任务数据
            cmd: 要执行的命令

        Returns:
            Tuple of (success, output, error)
        """
        try:

            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )

            success = result.returncode == 0
            output = result.stdout.strip()
            error = result.stderr.strip()

            if success:
                self.logger.info(f"Command succeeded: {cmd} \nOutput: {output}")
            else:
                self.logger.error(f"Command failed: {cmd} \nError: {error}")
            return success, output, error

        except subprocess.TimeoutExpired:
            error_msg = f"Command timeout: {cmd}"
            self.logger.error()
            return False, "", error_msg
        except Exception as e:
            self.logger.error(f"Command [{cmd}] execution error: {e}")
            return False, "", str(e)

    def _get_namespace(self, vlan_id: int) -> str:
        """获取命名空间名称"""
        return f"ns-vlan{vlan_id}"

    def _get_device_name(self, prefix: str, infix: str, vlan_id: int) -> str:
        """获取设备名称"""
        return f"v-{prefix}-{infix}-{vlan_id}"

    def _get_nft_table_name(self, vlan_id: int) -> str:
        """获取 nftables 表名称"""
        return f"nat-{vlan_id}"

    def _process_config_diff(self, task: TaskData, remote_config: SubnetConfigParams, local_config: SubnetConfigParams) -> bool:
        """处理配置差异

        Args:
            task: 任务数据
            remote_config: 远程配置
            local_config: 本地配置

        Returns:
            True if successful
        """
        try:
            # 1. 比对 subnet 配置
            if not self._process_subnet_diff(task, remote_config, local_config):
                return False

            # 2. 比对 monnet 配置
            if not self._process_monnet_diff(task, remote_config, local_config):
                return False

            # 3. 比对 eipnet 配置
            if not self._process_eipnet_diff(task, remote_config, local_config):
                return False

            return True

        except Exception as e:
            error_msg = f"Error processing config diff: {e}"
            self.logger.error(error_msg)
            if task:
                task.status = TaskStatus.ERROR
                task.message = error_msg
            return False

    def _process_subnet_diff(self, task: TaskData, remote_config: SubnetConfigParams, local_config: SubnetConfigParams) -> bool:
        """处理子网配置差异

        Args:
            task: 任务数据
            remote_config: 远程配置
            local_config: 本地配置

        Returns:
            True if successful
        """
        remote_subnet = remote_config.subnet
        local_subnet = local_config.subnet

        # 比对逻辑
        if not remote_subnet and local_subnet:
            # 远程为 null，本地不为 null，生成删除命令
            self.logger.info("Subnet: remote is null, local exists - deleting subnet")
            return self._delete_subnet(task, local_config)

        elif remote_subnet and not local_subnet:
            # 远程不为 null，本地为 null，生成创建命令
            self.logger.info("Subnet: remote exists, local is null - creating subnet")
            return self._create_subnet(task, remote_config)

        elif remote_subnet and local_subnet:
            # 两者都不为 null，检查内容是否一致
            if not self._subnet_configs_equal(remote_subnet, local_subnet):
                # 内容不一致，先删除后创建
                self.logger.info("Subnet: configs differ - recreating subnet")
                if not self._delete_subnet(task, local_config):
                    return False
                return self._create_subnet(task, remote_config)
            else:
                self.logger.info("Subnet: configs are identical - no changes needed")

        # 两者都为 null 或内容相同，无需操作
        return True

    def _process_monnet_diff(self, task: TaskData, remote_config: SubnetConfigParams, local_config: SubnetConfigParams) -> bool:
        """处理监控网络配置差异

        Args:
            task: 任务数据
            remote_config: 远程配置
            local_config: 本地配置

        Returns:
            True if successful
        """
        remote_monnet = remote_config.monnet
        local_monnet = local_config.monnet

        # 比对逻辑
        if not remote_monnet and local_monnet:
            # 远程为 null，本地不为 null，生成删除命令
            self.logger.info("Monnet: remote is null, local exists - deleting monnet")
            return self._delete_monnet(task, local_config)

        elif remote_monnet and not local_monnet:
            # 远程不为 null，本地为 null，生成创建命令
            self.logger.info("Monnet: remote exists, local is null - creating monnet")
            return self._create_monnet(task, remote_config)

        elif remote_monnet and local_monnet:
            # 两者都不为 null，检查内容是否一致
            if not self._monnet_configs_equal(remote_monnet, local_monnet):
                # 内容不一致，先删除后创建
                self.logger.info("Monnet: configs differ - recreating monnet")
                if not self._delete_monnet(task, local_config):
                    return False
                return self._create_monnet(task, remote_config)
            else:
                self.logger.info("Monnet: configs are identical - no changes needed")

        # 两者都为 null 或内容相同，无需操作
        return True

    def _process_eipnet_diff(self, task: TaskData, remote_config: SubnetConfigParams, local_config: SubnetConfigParams) -> bool:
        """处理 EIP 网络配置差异

        Args:
            task: 任务数据
            remote_config: 远程配置
            local_config: 本地配置

        Returns:
            True if successful
        """
        remote_eipnet = remote_config.eipnet
        local_eipnet = local_config.eipnet

        # 比对逻辑
        if not remote_eipnet and local_eipnet:
            # 远程为 null，本地不为 null，生成删除命令
            self.logger.info("Eipnet: remote is null, local exists - deleting eipnet")
            return self._delete_eipnet(task, local_config)

        elif remote_eipnet and not local_eipnet:
            # 远程不为 null，本地为 null，生成创建命令
            self.logger.info("Eipnet: remote exists, local is null - creating eipnet")
            return self._create_eipnet(task, remote_config)

        elif remote_eipnet and local_eipnet:
            # 两者都不为 null，检查内容是否一致
            if not self._eipnet_configs_equal(remote_eipnet, local_eipnet):
                # 内容不一致，先删除后创建
                self.logger.info("Eipnet: configs differ - recreating eipnet")
                if not self._delete_eipnet(task, local_config):
                    return False
                return self._create_eipnet(task, remote_config)
            else:
                self.logger.info("Eipnet: configs are identical - no changes needed")

        # 两者都为 null 或内容相同，无需操作
        return True

    def _subnet_configs_equal(self, config1: SubnetConfig, config2: SubnetConfig) -> bool:
        """比较两个子网配置是否相等"""
        if not config1 or not config2:
            return config1 == config2

        return (config1.gateway_ip == config2.gateway_ip and
                config1.pre_ip == config2.pre_ip)

    def _monnet_configs_equal(self, config1: MonnetConfig, config2: MonnetConfig) -> bool:
        """比较两个监控网络配置是否相等"""
        if not config1 or not config2:
            return config1 == config2

        return (config1.mon_ip == config2.mon_ip and
                config1.mon_m_ip == config2.mon_m_ip and
                config1.mon_m_ports == config2.mon_m_ports and
                config1.ext_m_ports == config2.ext_m_ports)

    def _eipnet_configs_equal(self, config1: EipnetConfig, config2: EipnetConfig) -> bool:
        """比较两个 EIP 网络配置是否相等"""
        if not config1 or not config2:
            return config1 == config2

        # 比较 gateway_ip
        if config1.gateway_ip != config2.gateway_ip:
            return False

        # 将 mounts 列表转换为可比较的集合
        mounts1_set = {(m.eip, m.internal_ip, m.rate, m.ceil) for m in config1.mounts}
        mounts2_set = {(m.eip, m.internal_ip, m.rate, m.ceil) for m in config2.mounts}

        # 比较 snat
        snat1_set = {(s.eip, s.gateway_ip, s.mask) for s in config1.snat}
        snat2_set = {(s.eip, s.gateway_ip, s.mask) for s in config2.snat}

        # 比较 dnat
        dnat1_set = {(d.eip, d.internal_ip, d.type, d.dport, d.port) for d in config1.dnat}
        dnat2_set = {(d.eip, d.internal_ip, d.type, d.dport, d.port) for d in config2.dnat}

        return mounts1_set == mounts2_set and snat1_set == snat2_set and dnat1_set == dnat2_set
