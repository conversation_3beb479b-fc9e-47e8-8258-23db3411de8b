"""
Database-based heartbeat manager for v-switch agent.
Sends heartbeat to API server instead of etcd.
"""

import logging
import threading
import time
import requests
from typing import Optional
from v_switch.config.agent_config import AgentConfig
from v_switch.agent.environment_checker import EnvironmentChecker


class DatabaseHeartbeatManager:
    """Database-based heartbeat manager that calls API server."""

    def __init__(self, config: AgentConfig, env_checker: EnvironmentChecker, api_server_url: str):
        """
        Initialize database heartbeat manager

        Args:
            config: Agent configuration
            env_checker: Environment checker instance
            api_server_url: API server base URL (e.g., "http://localhost:30090")
        """
        self.config = config
        self.env_checker = env_checker
        self.api_server_url = api_server_url.rstrip('/')
        self.logger = logging.getLogger(__name__)

        self._heartbeat_thread = None
        self._running = False
        self._current_status = "unknown"
        self._last_applied = 0

    def start(self) -> bool:
        """Start heartbeat manager.

        Returns:
            True if successful
        """
        try:
            if self._running:
                self.logger.warning("Heartbeat manager is already running")
                return True

            # Validate configuration
            if not self.config.agent.node_id:
                self.logger.error("Node ID is required for heartbeat")
                return False
            
            if not self.config.agent.group_id:
                self.logger.error("Group ID is required for heartbeat")
                return False
                
            if not self.config.agent.endpoint:
                self.logger.error("Endpoint is required for heartbeat")
                return False

            self._running = True
            self._heartbeat_thread = threading.Thread(
                target=self._heartbeat_loop,
                name="Heartbeat",
                daemon=True
            )
            self._heartbeat_thread.start()

            self.logger.info(f"Started database heartbeat manager for node {self.config.agent.node_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start heartbeat manager: {e}")
            return False

    def stop(self) -> None:
        """Stop heartbeat manager."""
        try:
            if not self._running:
                return

            self._running = False

            if self._heartbeat_thread and self._heartbeat_thread.is_alive():
                self._heartbeat_thread.join(timeout=5.0)

            self.logger.info("Stopped database heartbeat manager")

        except Exception as e:
            self.logger.error(f"Error stopping heartbeat manager: {e}")

    def update_last_applied(self, last_applied_id: int) -> None:
        """Update the last applied redo log ID.
        
        Args:
            last_applied_id: Last applied redo log ID
        """
        self._last_applied = last_applied_id
        self.logger.debug(f"Updated last_applied to {last_applied_id}")

    def _heartbeat_loop(self) -> None:
        """Main heartbeat loop"""
        while self._running:
            try:
                # Execute environment check
                env_ok, env_errors = self.env_checker.check_all()
                if not env_ok:
                    self._current_status = "error"
                    self.logger.error(f"Environment check failed: {env_errors}")
                else:
                    self._current_status = "running"
                    self.logger.debug("Environment check passed")

                # Send heartbeat to API server
                if not self._send_heartbeat():
                    self.logger.error("Failed to send heartbeat")

            except Exception as e:
                self.logger.error(f"Error in heartbeat loop: {e}")

            # Sleep until next heartbeat
            time.sleep(self.config.agent.heartbeat_interval)

    def _send_heartbeat(self) -> bool:
        """Send heartbeat to API server.

        Returns:
            True if successful
        """
        try:
            url = f"{self.api_server_url}/api/heartbeat"
            
            heartbeat_data = {
                "node_id": self.config.agent.node_id,
                "group_id": self.config.agent.group_id,
                "endpoint": self.config.agent.endpoint,
                "status": self._current_status,
                "last_applied": self._last_applied
            }

            response = requests.post(
                url,
                json=heartbeat_data,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )

            if response.status_code == 200:
                self.logger.debug(f"Successfully sent heartbeat for node {self.config.agent.node_id}")
                return True
            else:
                self.logger.error(f"API server returned status {response.status_code} for heartbeat")
                try:
                    error_detail = response.json().get('detail', 'Unknown error')
                    self.logger.error(f"Heartbeat error detail: {error_detail}")
                except:
                    pass
                return False

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error sending heartbeat to API server: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error sending heartbeat: {e}")
            return False

    def register_node(self) -> bool:
        """Register node with the API server on startup.
        
        Returns:
            True if successful
        """
        try:
            # Initial registration is handled by the first heartbeat
            return self._send_heartbeat()
            
        except Exception as e:
            self.logger.error(f"Error registering node: {e}")
            return False

    def is_running(self) -> bool:
        """Check if heartbeat manager is running.
        
        Returns:
            True if running
        """
        return self._running

    def get_current_status(self) -> str:
        """Get current agent status.
        
        Returns:
            Current status string
        """
        return self._current_status

    def get_last_applied(self) -> int:
        """Get last applied redo log ID.
        
        Returns:
            Last applied redo log ID
        """
        return self._last_applied
